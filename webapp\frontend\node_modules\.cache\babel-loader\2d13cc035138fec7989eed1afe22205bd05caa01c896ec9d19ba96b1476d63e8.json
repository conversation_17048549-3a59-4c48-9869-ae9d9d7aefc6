{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\UserPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, Snackbar, Alert, IconButton, ToggleButton, ToggleButtonGroup } from '@mui/material';\nimport { Construction as ConstructionIcon, Delete as DeleteIcon, Add as AddIcon, ContentCopy as ContentCopyIcon, Info as InfoIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon } from '@mui/icons-material';\nimport CantieriFilterableTable from '../components/cantieri/CantieriFilterableTable';\nimport EditCantiereDialog from '../components/cantieri/EditCantiereDialog';\nimport CreateCantiereDialog from '../components/cantieri/CreateCantiereDialog';\nimport PasswordManagementDialog from '../components/cantieri/PasswordManagementDialog';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserPage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser,\n    selectCantiere\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [openEditDialog, setOpenEditDialog] = useState(false);\n  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'cards'\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCantiereCreated = newCantiere => {\n    // Aggiorna la lista dei cantieri\n    setCantieri([...cantieri, newCantiere]);\n\n    // Mostra una notifica di successo\n    setNotification({\n      open: true,\n      message: `Cantiere ${newCantiere.commessa} creato con successo!\\nCodice univoco: ${newCantiere.codice_univoco}`,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.commessa || selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = cantiere => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage\n    selectCantiere(cantiere);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (event, newViewMode) => {\n    if (newViewMode !== null) {\n      setViewMode(newViewMode);\n    }\n  };\n\n  // Gestisce la copia del codice univoco\n  const handleCopyCode = codiceUnivoco => {\n    navigator.clipboard.writeText(codiceUnivoco);\n    setNotification({\n      open: true,\n      message: 'Codice univoco copiato negli appunti',\n      severity: 'success'\n    });\n  };\n\n  // Gestisce l'apertura del dialog di modifica cantiere\n  const handleOpenEditDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenEditDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di modifica cantiere\n  const handleCloseEditDialog = () => {\n    setOpenEditDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'apertura del dialog di gestione password\n  const handleOpenPasswordDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenPasswordDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di gestione password\n  const handleClosePasswordDialog = () => {\n    setOpenPasswordDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'aggiornamento di un cantiere\n  const handleCantiereUpdated = updatedCantiere => {\n    setCantieri(prevCantieri => prevCantieri.map(cantiere => cantiere.id_cantiere === updatedCantiere.id_cantiere ? updatedCantiere : cantiere));\n    setNotification({\n      open: true,\n      message: 'Cantiere aggiornato con successo!',\n      severity: 'success'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cantieri-page\",\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: isImpersonating && impersonatedUser ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}` : \"Visualizza e gestisci i tuoi cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [cantieri.length > 0 && /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n          value: viewMode,\n          exclusive: true,\n          onChange: handleViewModeChange,\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(ToggleButton, {\n            value: \"table\",\n            \"aria-label\": \"vista tabella\",\n            children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n            value: \"cards\",\n            \"aria-label\": \"vista schede\",\n            children: /*#__PURE__*/_jsxDEV(ViewModuleIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 24\n          }, this),\n          onClick: handleOpenCreateDialog,\n          children: \"Nuovo Cantiere\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cantieri...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this) : cantieri.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Nessun cantiere trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Crea un nuovo cantiere per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        className: \"primary-button\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 24\n        }, this),\n        onClick: handleOpenCreateDialog,\n        sx: {\n          mt: 2\n        },\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this) : viewMode === 'table' ? /*#__PURE__*/_jsxDEV(CantieriFilterableTable, {\n      cantieri: cantieri,\n      loading: loading,\n      onManageCavi: handleSelectCantiere,\n      onEdit: handleOpenEditDialog,\n      onDelete: handleOpenDeleteDialog,\n      onCopyCode: handleCopyCode,\n      onManagePassword: handleOpenPasswordDialog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"cantiere-header\",\n              children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cantiere.commessa || cantiere.nome\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Descrizione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this), \" \", cantiere.descrizione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mr: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 23\n                }, this), \" ********\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  setNotification({\n                    open: true,\n                    message: 'Per motivi di sicurezza, la password è visibile solo al momento della creazione del cantiere.',\n                    severity: 'info'\n                  });\n                },\n                title: \"Informazioni sulla password\",\n                children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mr: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Codice Univoco:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this), \" \", cantiere.codice_univoco || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleCopyCode(cantiere.codice_univoco),\n                title: \"Copia codice univoco\",\n                children: /*#__PURE__*/_jsxDEV(ContentCopyIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"primary-button\",\n              onClick: () => handleSelectCantiere(cantiere),\n              children: \"Gestione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"error-button\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenDeleteDialog(cantiere),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 15\n        }, this)\n      }, cantiere.id_cantiere, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CreateCantiereDialog, {\n      open: openCreateDialog,\n      onClose: handleCloseCreateDialog,\n      onCantiereCreated: handleCantiereCreated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDeleteDialog,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Elimina Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"ATTENZIONE: Sei sicuro di voler eliminare il cantiere \\\"\", (selectedCantiere === null || selectedCantiere === void 0 ? void 0 : selectedCantiere.commessa) || (selectedCantiere === null || selectedCantiere === void 0 ? void 0 : selectedCantiere.nome), \"\\\" e tutti i suoi dati? Questa operazione non pu\\xF2 essere annullata.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseDeleteDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCantiere,\n          variant: \"contained\",\n          className: \"error-button\",\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditCantiereDialog, {\n      open: openEditDialog,\n      onClose: handleCloseEditDialog,\n      cantiere: selectedCantiere,\n      onCantiereUpdated: handleCantiereUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PasswordManagementDialog, {\n      open: openPasswordDialog,\n      onClose: handleClosePasswordDialog,\n      cantiere: selectedCantiere\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            whiteSpace: 'pre-line'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPage, \"GelqP89SztI8L+fxxuqkx3wvwms=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = UserPage;\nexport default UserPage;\nvar _c;\n$RefreshReg$(_c, \"UserPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "TextField", "Snackbar", "<PERSON><PERSON>", "IconButton", "ToggleButton", "ToggleButtonGroup", "Construction", "ConstructionIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "ContentCopy", "ContentCopyIcon", "Info", "InfoIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "CantieriFilterableTable", "EditCantiereDialog", "CreateCantiereDialog", "PasswordManagementDialog", "useAuth", "useNavigate", "cantieriService", "jsxDEV", "_jsxDEV", "UserPage", "_s", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "selectCantiere", "navigate", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "openCreateDialog", "setOpenCreateDialog", "openDeleteDialog", "setOpenDeleteDialog", "openEditDialog", "setOpenEditDialog", "openPasswordDialog", "setOpenPasswordDialog", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "notification", "setNotification", "open", "message", "severity", "viewMode", "setViewMode", "fetchCantieri", "data", "role", "getUserCantieri", "id", "getMyCantieri", "err", "console", "handleOpenCreateDialog", "handleCloseCreateDialog", "handleOpenDeleteDialog", "cantiere", "handleCloseDeleteDialog", "handleInputChange", "e", "name", "value", "target", "setNewCantiereData", "newCantiereData", "handleCantiereCreated", "newCantiere", "commessa", "codice_univoco", "handleDeleteCantiere", "deleteCantiere", "id_cantiere", "filter", "c", "nome", "handleSelectCantiere", "log", "handleCloseNotification", "handleViewModeChange", "event", "newViewMode", "handleCopyCode", "codiceUnivoco", "navigator", "clipboard", "writeText", "handleOpenEditDialog", "handleCloseEditDialog", "handleOpenPasswordDialog", "handleClosePasswordDialog", "handleCantiereUpdated", "updatedCantiere", "prevCantieri", "map", "className", "children", "variant", "gutterBottom", "username", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "display", "justifyContent", "alignItems", "gap", "length", "exclusive", "onChange", "size", "startIcon", "onClick", "p", "textAlign", "color", "mt", "onManageCavi", "onEdit", "onDelete", "onCopyCode", "onManagePassword", "container", "spacing", "item", "xs", "sm", "md", "component", "descrizione", "mr", "title", "fontSize", "onClose", "onCantiereCreated", "onCantiereUpdated", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/UserPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  TextField,\n  Snackbar,\n  Alert,\n  IconButton,\n  ToggleButton,\n  ToggleButtonGroup\n} from '@mui/material';\nimport {\n  Construction as ConstructionIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon,\n  ContentCopy as ContentCopyIcon,\n  Info as InfoIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon\n} from '@mui/icons-material';\nimport CantieriFilterableTable from '../components/cantieri/CantieriFilterableTable';\nimport EditCantiereDialog from '../components/cantieri/EditCantiereDialog';\nimport CreateCantiereDialog from '../components/cantieri/CreateCantiereDialog';\nimport PasswordManagementDialog from '../components/cantieri/PasswordManagementDialog';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\n\nconst UserPage = () => {\n  const { user, isImpersonating, impersonatedUser, selectCantiere } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [openEditDialog, setOpenEditDialog] = useState(false);\n  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'cards'\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if (user?.role === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCantiereCreated = (newCantiere) => {\n    // Aggiorna la lista dei cantieri\n    setCantieri([...cantieri, newCantiere]);\n\n    // Mostra una notifica di successo\n    setNotification({\n      open: true,\n      message: `Cantiere ${newCantiere.commessa} creato con successo!\\nCodice univoco: ${newCantiere.codice_univoco}`,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.commessa || selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = (cantiere) => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage\n    selectCantiere(cantiere);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (event, newViewMode) => {\n    if (newViewMode !== null) {\n      setViewMode(newViewMode);\n    }\n  };\n\n  // Gestisce la copia del codice univoco\n  const handleCopyCode = (codiceUnivoco) => {\n    navigator.clipboard.writeText(codiceUnivoco);\n    setNotification({\n      open: true,\n      message: 'Codice univoco copiato negli appunti',\n      severity: 'success'\n    });\n  };\n\n  // Gestisce l'apertura del dialog di modifica cantiere\n  const handleOpenEditDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenEditDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di modifica cantiere\n  const handleCloseEditDialog = () => {\n    setOpenEditDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'apertura del dialog di gestione password\n  const handleOpenPasswordDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenPasswordDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di gestione password\n  const handleClosePasswordDialog = () => {\n    setOpenPasswordDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'aggiornamento di un cantiere\n  const handleCantiereUpdated = (updatedCantiere) => {\n    setCantieri(prevCantieri =>\n      prevCantieri.map(cantiere =>\n        cantiere.id_cantiere === updatedCantiere.id_cantiere\n          ? updatedCantiere\n          : cantiere\n      )\n    );\n    setNotification({\n      open: true,\n      message: 'Cantiere aggiornato con successo!',\n      severity: 'success'\n    });\n  };\n\n  return (\n    <Box className=\"cantieri-page\">\n      <Typography variant=\"h4\" gutterBottom>\n        {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"}\n      </Typography>\n\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"body1\">\n          {isImpersonating && impersonatedUser\n            ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}`\n            : \"Visualizza e gestisci i tuoi cantieri\"}\n        </Typography>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          {cantieri.length > 0 && (\n            <ToggleButtonGroup\n              value={viewMode}\n              exclusive\n              onChange={handleViewModeChange}\n              size=\"small\"\n            >\n              <ToggleButton value=\"table\" aria-label=\"vista tabella\">\n                <ViewListIcon />\n              </ToggleButton>\n              <ToggleButton value=\"cards\" aria-label=\"vista schede\">\n                <ViewModuleIcon />\n              </ToggleButton>\n            </ToggleButtonGroup>\n          )}\n          <Button\n            variant=\"contained\"\n            className=\"primary-button\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Box>\n      </Box>\n\n      {loading ? (\n        <Typography>Caricamento cantieri...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : cantieri.length === 0 ? (\n        <Paper sx={{ p: 3, textAlign: 'center' }}>\n          <Typography variant=\"h6\">Nessun cantiere trovato</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Crea un nuovo cantiere per iniziare\n          </Typography>\n          <Button\n            variant=\"contained\"\n            className=\"primary-button\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n            sx={{ mt: 2 }}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Paper>\n      ) : viewMode === 'table' ? (\n        <CantieriFilterableTable\n          cantieri={cantieri}\n          loading={loading}\n          onManageCavi={handleSelectCantiere}\n          onEdit={handleOpenEditDialog}\n          onDelete={handleOpenDeleteDialog}\n          onCopyCode={handleCopyCode}\n          onManagePassword={handleOpenPasswordDialog}\n        />\n      ) : (\n        <Grid container spacing={3}>\n          {cantieri.map((cantiere) => (\n            <Grid item xs={12} sm={6} md={4} key={cantiere.id_cantiere}>\n              <Card>\n                <CardContent>\n                  <Box className=\"cantiere-header\">\n                    <ConstructionIcon />\n                    <Typography variant=\"h6\" component=\"div\">\n                      {cantiere.commessa || cantiere.nome}\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    <strong>Descrizione:</strong> {cantiere.descrizione || 'N/A'}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1 }}>\n                      <strong>Password:</strong> ********\n                    </Typography>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => {\n                        setNotification({\n                          open: true,\n                          message: 'Per motivi di sicurezza, la password è visibile solo al momento della creazione del cantiere.',\n                          severity: 'info'\n                        });\n                      }}\n                      title=\"Informazioni sulla password\"\n                    >\n                      <InfoIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1 }}>\n                      <strong>Codice Univoco:</strong> {cantiere.codice_univoco || 'N/A'}\n                    </Typography>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleCopyCode(cantiere.codice_univoco)}\n                      title=\"Copia codice univoco\"\n                    >\n                      <ContentCopyIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    variant=\"contained\"\n                    className=\"primary-button\"\n                    onClick={() => handleSelectCantiere(cantiere)}\n                  >\n                    Gestione Cavi\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    className=\"error-button\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleOpenDeleteDialog(cantiere)}\n                  >\n                    Elimina\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Dialog per creare un nuovo cantiere */}\n      <CreateCantiereDialog\n        open={openCreateDialog}\n        onClose={handleCloseCreateDialog}\n        onCantiereCreated={handleCantiereCreated}\n      />\n\n      {/* Dialog per eliminare un cantiere */}\n      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>\n        <DialogTitle>Elimina Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            ATTENZIONE: Sei sicuro di voler eliminare il cantiere \"{selectedCantiere?.commessa || selectedCantiere?.nome}\" e tutti i suoi dati?\n            Questa operazione non può essere annullata.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseDeleteDialog}>Annulla</Button>\n          <Button onClick={handleDeleteCantiere} variant=\"contained\" className=\"error-button\">\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per modificare un cantiere */}\n      <EditCantiereDialog\n        open={openEditDialog}\n        onClose={handleCloseEditDialog}\n        cantiere={selectedCantiere}\n        onCantiereUpdated={handleCantiereUpdated}\n      />\n\n      {/* Dialog per gestire la password */}\n      <PasswordManagementDialog\n        open={openPasswordDialog}\n        onClose={handleClosePasswordDialog}\n        cantiere={selectedCantiere}\n      />\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          <div style={{ whiteSpace: 'pre-line' }}>{notification.message}</div>\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default UserPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,iBAAiB,QACZ,eAAe;AACtB,SACEC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,uBAAuB,MAAM,gDAAgD;AACpF,OAAOC,kBAAkB,MAAM,2CAA2C;AAC1E,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,wBAAwB,MAAM,iDAAiD;AACtF,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC7E,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC;IAC/CqE,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyE,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFpB,UAAU,CAAC,IAAI,CAAC;QAChB,IAAIqB,IAAI;;QAER;QACA,IAAI,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI,MAAK,OAAO,IAAI7B,eAAe,IAAIC,gBAAgB,EAAE;UACjE;UACA2B,IAAI,GAAG,MAAMlC,eAAe,CAACoC,eAAe,CAAC7B,gBAAgB,CAAC8B,EAAE,CAAC;QACnE,CAAC,MAAM;UACL;UACAH,IAAI,GAAG,MAAMlC,eAAe,CAACsC,aAAa,CAAC,CAAC;QAC9C;QAEA3B,WAAW,CAACuB,IAAI,CAAC;MACnB,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAAC1B,KAAK,CAAC,sCAAsC,EAAEyB,GAAG,CAAC;QAC1DxB,QAAQ,CAAC,qDAAqD,CAAC;MACjE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC5B,IAAI,EAAEC,eAAe,EAAEC,gBAAgB,CAAC,CAAC;;EAE7C;EACA,MAAMkC,sBAAsB,GAAGA,CAAA,KAAM;IACnCxB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyB,uBAAuB,GAAGA,CAAA,KAAM;IACpCzB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM0B,sBAAsB,GAAIC,QAAQ,IAAK;IAC3CnB,mBAAmB,CAACmB,QAAQ,CAAC;IAC7BzB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0B,uBAAuB,GAAGA,CAAA,KAAM;IACpC1B,mBAAmB,CAAC,KAAK,CAAC;IAC1BM,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCC,kBAAkB,CAAC;MACjB,GAAGC,eAAe;MAClB,CAACJ,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,qBAAqB,GAAIC,WAAW,IAAK;IAC7C;IACA3C,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE4C,WAAW,CAAC,CAAC;;IAEvC;IACA3B,eAAe,CAAC;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,YAAYyB,WAAW,CAACC,QAAQ,0CAA0CD,WAAW,CAACE,cAAc,EAAE;MAC/G1B,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACjC,gBAAgB,EAAE;IAEvB,IAAI;MACF,MAAMxB,eAAe,CAAC0D,cAAc,CAAClC,gBAAgB,CAACmC,WAAW,CAAC;;MAElE;MACAhD,WAAW,CAACD,QAAQ,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,WAAW,KAAKnC,gBAAgB,CAACmC,WAAW,CAAC,CAAC;;MAEjF;MACAd,uBAAuB,CAAC,CAAC;;MAEzB;MACAlB,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYL,gBAAgB,CAAC+B,QAAQ,IAAI/B,gBAAgB,CAACsC,IAAI,0BAA0B;QACjGhC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAAC1B,KAAK,CAAC,yCAAyC,EAAEyB,GAAG,CAAC;MAC7DZ,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMiC,oBAAoB,GAAInB,QAAQ,IAAK;IACzCJ,OAAO,CAACwB,GAAG,CAAC,uBAAuB,EAAEpB,QAAQ,CAAC;;IAE9C;IACApC,cAAc,CAACoC,QAAQ,CAAC;;IAExB;IACAnC,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC;;EAED;EACA,MAAMwD,uBAAuB,GAAGA,CAAA,KAAM;IACpCtC,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMsC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IACnD,IAAIA,WAAW,KAAK,IAAI,EAAE;MACxBpC,WAAW,CAACoC,WAAW,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,aAAa,IAAK;IACxCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,aAAa,CAAC;IAC5C3C,eAAe,CAAC;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,sCAAsC;MAC/CC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4C,oBAAoB,GAAI9B,QAAQ,IAAK;IACzCnB,mBAAmB,CAACmB,QAAQ,CAAC;IAC7BvB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMsD,qBAAqB,GAAGA,CAAA,KAAM;IAClCtD,iBAAiB,CAAC,KAAK,CAAC;IACxBI,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMmD,wBAAwB,GAAIhC,QAAQ,IAAK;IAC7CnB,mBAAmB,CAACmB,QAAQ,CAAC;IAC7BrB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMsD,yBAAyB,GAAGA,CAAA,KAAM;IACtCtD,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMqD,qBAAqB,GAAIC,eAAe,IAAK;IACjDpE,WAAW,CAACqE,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACrC,QAAQ,IACvBA,QAAQ,CAACe,WAAW,KAAKoB,eAAe,CAACpB,WAAW,GAChDoB,eAAe,GACfnC,QACN,CACF,CAAC;IACDjB,eAAe,CAAC;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,mCAAmC;MAC5CC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACE5B,OAAA,CAACzC,GAAG;IAACyH,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BjF,OAAA,CAACxC,UAAU;MAAC0H,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClC7E,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAAC+E,QAAQ,EAAE,GAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC,eAEbxF,OAAA,CAACzC,GAAG;MAACkI,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAZ,QAAA,gBACzFjF,OAAA,CAACxC,UAAU;QAAC0H,OAAO,EAAC,OAAO;QAAAD,QAAA,EACxB7E,eAAe,IAAIC,gBAAgB,GAChC,uCAAuCA,gBAAgB,CAAC+E,QAAQ,EAAE,GAClE;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACbxF,OAAA,CAACzC,GAAG;QAACkI,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAb,QAAA,GACxDzE,QAAQ,CAACuF,MAAM,GAAG,CAAC,iBAClB/F,OAAA,CAACvB,iBAAiB;UAChBsE,KAAK,EAAElB,QAAS;UAChBmE,SAAS;UACTC,QAAQ,EAAEjC,oBAAqB;UAC/BkC,IAAI,EAAC,OAAO;UAAAjB,QAAA,gBAEZjF,OAAA,CAACxB,YAAY;YAACuE,KAAK,EAAC,OAAO;YAAC,cAAW,eAAe;YAAAkC,QAAA,eACpDjF,OAAA,CAACX,YAAY;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACfxF,OAAA,CAACxB,YAAY;YAACuE,KAAK,EAAC,OAAO;YAAC,cAAW,cAAc;YAAAkC,QAAA,eACnDjF,OAAA,CAACT,cAAc;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACpB,eACDxF,OAAA,CAACtC,MAAM;UACLwH,OAAO,EAAC,WAAW;UACnBF,SAAS,EAAC,gBAAgB;UAC1BmB,SAAS,eAAEnG,OAAA,CAACjB,OAAO;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBY,OAAO,EAAE7D,sBAAuB;UAAA0C,QAAA,EACjC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL9E,OAAO,gBACNV,OAAA,CAACxC,UAAU;MAAAyH,QAAA,EAAC;IAAuB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC9C5E,KAAK,gBACPZ,OAAA,CAAC1B,KAAK;MAACsD,QAAQ,EAAC,OAAO;MAAAqD,QAAA,EAAErE;IAAK;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrChF,QAAQ,CAACuF,MAAM,KAAK,CAAC,gBACvB/F,OAAA,CAACvC,KAAK;MAACgI,EAAE,EAAE;QAAEY,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAArB,QAAA,gBACvCjF,OAAA,CAACxC,UAAU;QAAC0H,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DxF,OAAA,CAACxC,UAAU;QAAC0H,OAAO,EAAC,OAAO;QAACqB,KAAK,EAAC,gBAAgB;QAAAtB,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAACtC,MAAM;QACLwH,OAAO,EAAC,WAAW;QACnBF,SAAS,EAAC,gBAAgB;QAC1BmB,SAAS,eAAEnG,OAAA,CAACjB,OAAO;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBY,OAAO,EAAE7D,sBAAuB;QAChCkD,EAAE,EAAE;UAAEe,EAAE,EAAE;QAAE,CAAE;QAAAvB,QAAA,EACf;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,GACN3D,QAAQ,KAAK,OAAO,gBACtB7B,OAAA,CAACR,uBAAuB;MACtBgB,QAAQ,EAAEA,QAAS;MACnBE,OAAO,EAAEA,OAAQ;MACjB+F,YAAY,EAAE5C,oBAAqB;MACnC6C,MAAM,EAAElC,oBAAqB;MAC7BmC,QAAQ,EAAElE,sBAAuB;MACjCmE,UAAU,EAAEzC,cAAe;MAC3B0C,gBAAgB,EAAEnC;IAAyB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,gBAEFxF,OAAA,CAACrC,IAAI;MAACmJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA9B,QAAA,EACxBzE,QAAQ,CAACuE,GAAG,CAAErC,QAAQ,iBACrB1C,OAAA,CAACrC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eAC9BjF,OAAA,CAACpC,IAAI;UAAAqH,QAAA,gBACHjF,OAAA,CAACnC,WAAW;YAAAoH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACyH,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BjF,OAAA,CAACrB,gBAAgB;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBxF,OAAA,CAACxC,UAAU;gBAAC0H,OAAO,EAAC,IAAI;gBAACkC,SAAS,EAAC,KAAK;gBAAAnC,QAAA,EACrCvC,QAAQ,CAACW,QAAQ,IAAIX,QAAQ,CAACkB;cAAI;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxF,OAAA,CAACxC,UAAU;cAAC0H,OAAO,EAAC,OAAO;cAACqB,KAAK,EAAC,gBAAgB;cAACd,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBAC/DjF,OAAA;gBAAAiF,QAAA,EAAQ;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC9C,QAAQ,CAAC2E,WAAW,IAAI,KAAK;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACbxF,OAAA,CAACzC,GAAG;cAACkI,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBACxDjF,OAAA,CAACxC,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACqB,KAAK,EAAC,gBAAgB;gBAACd,EAAE,EAAE;kBAAE6B,EAAE,EAAE;gBAAE,CAAE;gBAAArC,QAAA,gBAC/DjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,aAC5B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbxF,OAAA,CAACzB,UAAU;gBACT2H,IAAI,EAAC,OAAO;gBACZE,OAAO,EAAEA,CAAA,KAAM;kBACb3E,eAAe,CAAC;oBACdC,IAAI,EAAE,IAAI;oBACVC,OAAO,EAAE,+FAA+F;oBACxGC,QAAQ,EAAE;kBACZ,CAAC,CAAC;gBACJ,CAAE;gBACF2F,KAAK,EAAC,6BAA6B;gBAAAtC,QAAA,eAEnCjF,OAAA,CAACb,QAAQ;kBAACqI,QAAQ,EAAC;gBAAO;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxF,OAAA,CAACzC,GAAG;cAACkI,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBACxDjF,OAAA,CAACxC,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACqB,KAAK,EAAC,gBAAgB;gBAACd,EAAE,EAAE;kBAAE6B,EAAE,EAAE;gBAAE,CAAE;gBAAArC,QAAA,gBAC/DjF,OAAA;kBAAAiF,QAAA,EAAQ;gBAAe;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9C,QAAQ,CAACY,cAAc,IAAI,KAAK;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACbxF,OAAA,CAACzB,UAAU;gBACT2H,IAAI,EAAC,OAAO;gBACZE,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAACzB,QAAQ,CAACY,cAAc,CAAE;gBACvDiE,KAAK,EAAC,sBAAsB;gBAAAtC,QAAA,eAE5BjF,OAAA,CAACf,eAAe;kBAACuI,QAAQ,EAAC;gBAAO;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACdxF,OAAA,CAAClC,WAAW;YAAAmH,QAAA,gBACVjF,OAAA,CAACtC,MAAM;cACLwH,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,gBAAgB;cAC1BoB,OAAO,EAAEA,CAAA,KAAMvC,oBAAoB,CAACnB,QAAQ,CAAE;cAAAuC,QAAA,EAC/C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxF,OAAA,CAACtC,MAAM;cACLwH,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,cAAc;cACxBmB,SAAS,eAAEnG,OAAA,CAACnB,UAAU;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BY,OAAO,EAAEA,CAAA,KAAM3D,sBAAsB,CAACC,QAAQ,CAAE;cAAAuC,QAAA,EACjD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA5D6B9C,QAAQ,CAACe,WAAW;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6DpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDxF,OAAA,CAACN,oBAAoB;MACnBgC,IAAI,EAAEZ,gBAAiB;MACvB2G,OAAO,EAAEjF,uBAAwB;MACjCkF,iBAAiB,EAAEvE;IAAsB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFxF,OAAA,CAACjC,MAAM;MAAC2D,IAAI,EAAEV,gBAAiB;MAACyG,OAAO,EAAE9E,uBAAwB;MAAAsC,QAAA,gBAC/DjF,OAAA,CAAC7B,WAAW;QAAA8G,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3CxF,OAAA,CAAC/B,aAAa;QAAAgH,QAAA,eACZjF,OAAA,CAAC9B,iBAAiB;UAAA+G,QAAA,GAAC,0DACsC,EAAC,CAAA3D,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE+B,QAAQ,MAAI/B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsC,IAAI,GAAC,wEAE/G;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBxF,OAAA,CAAChC,aAAa;QAAAiH,QAAA,gBACZjF,OAAA,CAACtC,MAAM;UAACwH,OAAO,EAAC,WAAW;UAACkB,OAAO,EAAEzD,uBAAwB;UAAAsC,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9ExF,OAAA,CAACtC,MAAM;UAAC0I,OAAO,EAAE7C,oBAAqB;UAAC2B,OAAO,EAAC,WAAW;UAACF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAEpF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTxF,OAAA,CAACP,kBAAkB;MACjBiC,IAAI,EAAER,cAAe;MACrBuG,OAAO,EAAEhD,qBAAsB;MAC/B/B,QAAQ,EAAEpB,gBAAiB;MAC3BqG,iBAAiB,EAAE/C;IAAsB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFxF,OAAA,CAACL,wBAAwB;MACvB+B,IAAI,EAAEN,kBAAmB;MACzBqG,OAAO,EAAE9C,yBAA0B;MACnCjC,QAAQ,EAAEpB;IAAiB;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGFxF,OAAA,CAAC3B,QAAQ;MACPqD,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxBkG,gBAAgB,EAAE,IAAK;MACvBH,OAAO,EAAE1D,uBAAwB;MACjC8D,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA9C,QAAA,eAE3DjF,OAAA,CAAC1B,KAAK;QAACmJ,OAAO,EAAE1D,uBAAwB;QAACnC,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAAC6D,EAAE,EAAE;UAAEuC,KAAK,EAAE;QAAO,CAAE;QAAA/C,QAAA,eAC9FjF,OAAA;UAAKiI,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAW,CAAE;UAAAjD,QAAA,EAAEzD,YAAY,CAACG;QAAO;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACtF,EAAA,CAlYID,QAAQ;EAAA,QACwDL,OAAO,EAC1DC,WAAW;AAAA;AAAAsI,EAAA,GAFxBlI,QAAQ;AAoYd,eAAeA,QAAQ;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
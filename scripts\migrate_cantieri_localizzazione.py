#!/usr/bin/env python3
"""
Script di migrazione per ristrutturare la tabella cantieri con localizzazione geografica.

Modifiche alla struttura:
- Rinomina 'nome' → 'commessa'
- Aggiunge: nome_cliente, indirizzo_cantiere, citta_cantiere, nazione_cantiere
- Rimuove: progetto_commessa, codice_progetto (duplicati)
- Mantiene: riferimenti_normativi, documentazione_progetto

Autore: Sistema CMS
Data: 2025-01-06
"""

import os
import sys
import logging
import psycopg2
from datetime import datetime

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migrate_cantieri_localizzazione.log'),
        logging.StreamHandler()
    ]
)

# Configurazione database
DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': os.environ.get('DB_PORT', '5432'),
    'database': os.environ.get('DB_NAME', 'cantieri'),
    'user': os.environ.get('DB_USER', 'postgres'),
    'password': os.environ.get('DB_PASSWORD', 'Taranto')
}

def check_column_exists(cursor, table_name, column_name):
    """Verifica se una colonna esiste in una tabella."""
    cursor.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s
    """, (table_name, column_name))
    return cursor.fetchone() is not None

def backup_data(cursor):
    """Crea un backup dei dati esistenti prima della migrazione."""
    logging.info("🔄 Backup dati esistenti...")
    
    try:
        # Crea tabella di backup se non esiste
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS cantieri_backup_pre_localizzazione AS 
            SELECT * FROM cantieri WHERE 1=0
        """)
        
        # Svuota la tabella di backup se esiste già
        cursor.execute("DELETE FROM cantieri_backup_pre_localizzazione")
        
        # Copia tutti i dati attuali
        cursor.execute("""
            INSERT INTO cantieri_backup_pre_localizzazione 
            SELECT * FROM cantieri
        """)
        
        cursor.execute("SELECT COUNT(*) FROM cantieri_backup_pre_localizzazione")
        backup_count = cursor.fetchone()[0]
        logging.info(f"✅ Backup completato: {backup_count} record salvati")
        
    except Exception as e:
        logging.error(f"❌ Errore durante il backup: {e}")
        raise

def migrate_cantieri_structure(cursor):
    """Migra la struttura della tabella cantieri con localizzazione geografica."""
    logging.info("🔄 Migrazione struttura tabella cantieri...")
    
    # 1. Rinomina colonna 'nome' → 'commessa'
    if check_column_exists(cursor, 'cantieri', 'nome') and not check_column_exists(cursor, 'cantieri', 'commessa'):
        try:
            cursor.execute("ALTER TABLE cantieri RENAME COLUMN nome TO commessa")
            logging.info("✅ Rinominata colonna 'nome' → 'commessa'")
        except Exception as e:
            logging.error(f"❌ Errore rinominando colonna 'nome': {e}")
            raise
    elif check_column_exists(cursor, 'cantieri', 'commessa'):
        logging.info("ℹ️ Colonna 'commessa' già esistente")
    
    # 2. Aggiungi nuovi campi di localizzazione
    new_location_columns = [
        ("nome_cliente", "TEXT", "Nome del cliente del cantiere"),
        ("indirizzo_cantiere", "TEXT", "Indirizzo completo del cantiere"),
        ("citta_cantiere", "TEXT", "Città del cantiere"),
        ("nazione_cantiere", "TEXT", "Nazione del cantiere")
    ]
    
    for column_name, column_type, description in new_location_columns:
        if not check_column_exists(cursor, 'cantieri', column_name):
            try:
                cursor.execute(f"ALTER TABLE cantieri ADD COLUMN {column_name} {column_type}")
                logging.info(f"✅ Aggiunta colonna '{column_name}' - {description}")
            except Exception as e:
                logging.error(f"❌ Errore aggiungendo colonna '{column_name}': {e}")
                raise
        else:
            logging.info(f"ℹ️ Colonna '{column_name}' già esistente")
    
    # 3. Aggiungi campi normativi se non esistono
    normative_columns = [
        ("riferimenti_normativi", "TEXT", "Normative e specifiche applicate"),
        ("documentazione_progetto", "TEXT", "Riferimenti a schemi e layout")
    ]
    
    for column_name, column_type, description in normative_columns:
        if not check_column_exists(cursor, 'cantieri', column_name):
            try:
                cursor.execute(f"ALTER TABLE cantieri ADD COLUMN {column_name} {column_type}")
                logging.info(f"✅ Aggiunta colonna '{column_name}' - {description}")
            except Exception as e:
                logging.error(f"❌ Errore aggiungendo colonna '{column_name}': {e}")
                raise
        else:
            logging.info(f"ℹ️ Colonna '{column_name}' già esistente")

def set_default_values(cursor):
    """Imposta valori di default per i cantieri esistenti."""
    logging.info("🔄 Impostazione valori di default per cantieri esistenti...")
    
    try:
        # Imposta valori di default per i nuovi campi
        cursor.execute("""
            UPDATE cantieri 
            SET 
                nome_cliente = COALESCE(nome_cliente, 'Cliente da definire'),
                indirizzo_cantiere = COALESCE(indirizzo_cantiere, 'Indirizzo da definire'),
                citta_cantiere = COALESCE(citta_cantiere, 'Città da definire'),
                nazione_cantiere = COALESCE(nazione_cantiere, 'Italia'),
                riferimenti_normativi = COALESCE(riferimenti_normativi, 'CEI 64-8 Parte 6; IEC 60364-6'),
                documentazione_progetto = COALESCE(documentazione_progetto, 'Schemi elettrici e layout di progetto')
            WHERE 
                nome_cliente IS NULL 
                OR indirizzo_cantiere IS NULL 
                OR citta_cantiere IS NULL 
                OR nazione_cantiere IS NULL
                OR riferimenti_normativi IS NULL 
                OR documentazione_progetto IS NULL
        """)
        
        rows_updated = cursor.rowcount
        logging.info(f"✅ Aggiornati {rows_updated} cantieri con valori di default")
        
    except Exception as e:
        logging.error(f"❌ Errore impostando valori di default: {e}")
        raise

def cleanup_old_columns(cursor):
    """Rimuove le colonne obsolete dopo la migrazione."""
    logging.info("🔄 Rimozione colonne obsolete...")
    
    # Colonne da rimuovere (duplicati)
    columns_to_remove = ['progetto_commessa', 'codice_progetto']
    
    for column_name in columns_to_remove:
        if check_column_exists(cursor, 'cantieri', column_name):
            try:
                cursor.execute(f"ALTER TABLE cantieri DROP COLUMN {column_name}")
                logging.info(f"✅ Rimossa colonna obsoleta '{column_name}'")
            except Exception as e:
                logging.error(f"❌ Errore rimuovendo colonna '{column_name}': {e}")
                raise
        else:
            logging.info(f"ℹ️ Colonna '{column_name}' già rimossa")

def verify_migration(cursor):
    """Verifica che la migrazione sia stata completata correttamente."""
    logging.info("🔍 Verifica migrazione...")
    
    # Lista dei campi che devono esistere dopo la migrazione
    required_columns = [
        'commessa', 'nome_cliente', 'indirizzo_cantiere', 
        'citta_cantiere', 'nazione_cantiere',
        'riferimenti_normativi', 'documentazione_progetto'
    ]
    
    missing_columns = []
    for column in required_columns:
        if not check_column_exists(cursor, 'cantieri', column):
            missing_columns.append(column)
    
    # Verifica che le colonne obsolete siano state rimosse
    obsolete_columns = ['progetto_commessa', 'codice_progetto']
    existing_obsolete = []
    for column in obsolete_columns:
        if check_column_exists(cursor, 'cantieri', column):
            existing_obsolete.append(column)
    
    if missing_columns:
        logging.error(f"❌ Migrazione incompleta. Colonne mancanti: {missing_columns}")
        raise Exception(f"Migrazione fallita: colonne mancanti {missing_columns}")
    
    if existing_obsolete:
        logging.warning(f"⚠️ Colonne obsolete ancora presenti: {existing_obsolete}")
    
    logging.info("✅ Migrazione completata con successo")
    
    # Mostra statistiche
    cursor.execute("SELECT COUNT(*) FROM cantieri")
    total_cantieri = cursor.fetchone()[0]
    logging.info(f"📊 Totale cantieri migrati: {total_cantieri}")

def main():
    """Funzione principale di migrazione."""
    logging.info("🚀 Avvio migrazione localizzazione cantieri")
    logging.info(f"📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Connessione al database
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = False
        cursor = conn.cursor()
        
        # Esegui migrazione
        backup_data(cursor)
        migrate_cantieri_structure(cursor)
        set_default_values(cursor)
        cleanup_old_columns(cursor)
        verify_migration(cursor)
        
        # Commit delle modifiche
        conn.commit()
        logging.info("✅ Migrazione completata e confermata")
        
    except Exception as e:
        logging.error(f"❌ Errore durante la migrazione: {e}")
        if 'conn' in locals():
            conn.rollback()
            logging.info("🔄 Rollback eseguito")
        sys.exit(1)
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        logging.info("🔌 Connessione database chiusa")

if __name__ == "__main__":
    main()

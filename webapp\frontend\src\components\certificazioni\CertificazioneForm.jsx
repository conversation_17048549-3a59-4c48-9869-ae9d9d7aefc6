import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Alert,
  Divider,
  Stack,
  CircularProgress
} from '@mui/material';
import { Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';

import { apiService } from '../../services/apiService';
import weatherService from '../../services/weatherService';

function CertificazioneForm({ cantiereId, certificazione, strumenti, onSuccess, onCancel }) {
  const [formData, setFormData] = useState({
    id_cavo: '',
    id_operatore: '',
    strumento_utilizzato: '',
    id_strumento: '',
    lunghezza_misurata: '',
    valore_continuita: 'OK',
    valore_isolamento: '500',
    valore_resistenza: 'OK',
    note: ''
  });

  const [cavi, setCavi] = useState([]);
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [weatherData, setWeatherData] = useState(null);
  const [weatherLoading, setWeatherLoading] = useState(false);

  useEffect(() => {
    loadCavi();
    loadWeatherData();
    if (certificazione) {
      setFormData({
        id_cavo: certificazione.id_cavo || '',
        id_operatore: certificazione.id_operatore || '',
        strumento_utilizzato: certificazione.strumento_utilizzato || '',
        id_strumento: certificazione.id_strumento || '',
        lunghezza_misurata: certificazione.lunghezza_misurata || '',
        valore_continuita: certificazione.valore_continuita || 'OK',
        valore_isolamento: certificazione.valore_isolamento || '500',
        valore_resistenza: certificazione.valore_resistenza || 'OK',
        note: certificazione.note || ''
      });
    }
  }, [certificazione, cantiereId]);

  const loadCavi = async () => {
    try {
      // Carica solo i cavi installati che non hanno già una certificazione
      const caviData = await apiService.getCavi(cantiereId);
      const caviInstallati = caviData.filter(cavo => 
        cavo.stato_installazione === 'INSTALLATO'
      );
      setCavi(caviInstallati);

      // Se stiamo modificando, trova il cavo selezionato
      if (certificazione) {
        const cavo = caviInstallati.find(c => c.id_cavo === certificazione.id_cavo);
        setSelectedCavo(cavo);
      }
    } catch (err) {
      console.error('Errore nel caricamento dei cavi:', err);
      setError('Errore nel caricamento dei cavi');
    }
  };

  const loadWeatherData = async () => {
    try {
      setWeatherLoading(true);
      const data = await weatherService.getFormattedWeatherForCantiere(cantiereId);
      setWeatherData(data);
    } catch (error) {
      console.error('Errore nel caricamento dati meteorologici:', error);
      setWeatherData({
        temperature: 'N/D',
        humidity: 'N/D',
        displayText: 'Dati meteorologici non disponibili',
        isDemo: true,
        success: false
      });
    } finally {
      setWeatherLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCavoChange = (event, newValue) => {
    setSelectedCavo(newValue);
    if (newValue) {
      setFormData(prev => ({
        ...prev,
        id_cavo: newValue.id_cavo,
        lunghezza_misurata: newValue.metratura_reale || ''
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        id_cavo: '',
        lunghezza_misurata: ''
      }));
    }
  };

  const handleStrumentoChange = (event) => {
    const strumentoId = event.target.value;
    handleInputChange('id_strumento', strumentoId);
    
    if (strumentoId) {
      const strumento = strumenti.find(s => s.id_strumento === strumentoId);
      if (strumento) {
        handleInputChange('strumento_utilizzato', `${strumento.nome} ${strumento.marca} ${strumento.modello}`);
      }
    } else {
      handleInputChange('strumento_utilizzato', '');
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    if (!formData.id_cavo) {
      setError('Seleziona un cavo da certificare');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const submitData = {
        ...formData,
        id_strumento: formData.id_strumento || null,
        lunghezza_misurata: formData.lunghezza_misurata ? parseFloat(formData.lunghezza_misurata) : null,
        // Aggiungi i dati meteorologici se disponibili
        temperatura_prova: weatherData?.temperature || null,
        umidita_prova: weatherData?.humidity || null
      };

      if (certificazione) {
        await apiService.updateCertificazione(cantiereId, certificazione.id_certificazione, submitData);
        onSuccess('Certificazione aggiornata con successo');
      } else {
        await apiService.createCertificazione(cantiereId, submitData);
        onSuccess('Certificazione creata con successo');
      }
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.response?.data?.detail || 'Errore nel salvataggio della certificazione');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper sx={{ p: 3, borderRadius: 2, boxShadow: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ color: '#2196f3', fontWeight: 'bold' }}>
        {certificazione ? 'Modifica Certificazione' : 'Nuova Certificazione'}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2, borderRadius: 2 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Selezione Cavo */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Selezione Cavo
            </Typography>
            <Autocomplete
              value={selectedCavo}
              onChange={handleCavoChange}
              options={cavi}
              getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia || ''} ${option.sezione || ''}`}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Cavo da certificare"
                  required
                  fullWidth
                />
              )}
              disabled={!!certificazione} // Non modificabile se stiamo editando
            />
            {selectedCavo && (
              <Box sx={{ mt: 1, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2">
                  <strong>Partenza:</strong> {selectedCavo.ubicazione_partenza || '-'} | 
                  <strong> Arrivo:</strong> {selectedCavo.ubicazione_arrivo || '-'} | 
                  <strong> Metri Teorici:</strong> {selectedCavo.metri_teorici || '-'} | 
                  <strong> Metri Reali:</strong> {selectedCavo.metratura_reale || '-'}
                </Typography>
              </Box>
            )}
          </Grid>

          <Grid item xs={12}>
            <Divider />
          </Grid>

          {/* Informazioni Operatore e Strumento */}
          <Grid item xs={12} md={6}>
            <TextField
              label="Operatore"
              value={formData.id_operatore}
              onChange={(e) => handleInputChange('id_operatore', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Strumento Certificato</InputLabel>
              <Select
                value={formData.id_strumento}
                onChange={handleStrumentoChange}
                label="Strumento Certificato"
              >
                <MenuItem value="">Nessuno</MenuItem>
                {strumenti.map((strumento) => (
                  <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>
                    {strumento.nome} {strumento.marca} {strumento.modello}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Descrizione Strumento (alternativa)"
              value={formData.strumento_utilizzato}
              onChange={(e) => handleInputChange('strumento_utilizzato', e.target.value)}
              fullWidth
              helperText="Utilizzare solo se lo strumento non è presente nell'elenco sopra"
            />
          </Grid>

          <Grid item xs={12}>
            <Divider />
          </Grid>

          {/* Condizioni Ambientali */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Condizioni Ambientali (Rilevate Automaticamente)
            </Typography>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                bgcolor: weatherData?.isDemo ? 'warning.light' : 'success.light',
                borderColor: weatherData?.isDemo ? 'warning.main' : 'success.main'
              }}
            >
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box>
                  {weatherLoading ? (
                    <CircularProgress size={20} />
                  ) : (
                    <Typography variant="h6" color="text.primary">
                      🌤️
                    </Typography>
                  )}
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body1" fontWeight="bold">
                    {weatherLoading ? 'Caricamento dati meteorologici...' : (
                      weatherData ? weatherData.displayText : 'Dati meteorologici non disponibili'
                    )}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {weatherData?.isDemo ? (
                      '⚠️ Dati demo - Verifica configurazione API o localizzazione cantiere'
                    ) : weatherData?.success ? (
                      '✅ Dati rilevati automaticamente dalla localizzazione del cantiere'
                    ) : (
                      '❌ Impossibile recuperare dati meteorologici reali'
                    )}
                  </Typography>
                </Box>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={loadWeatherData}
                  disabled={weatherLoading}
                >
                  Aggiorna
                </Button>
              </Stack>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Divider />
          </Grid>

          {/* Misurazioni */}
          <Grid item xs={12} md={6}>
            <TextField
              label="Lunghezza Misurata (m)"
              type="number"
              value={formData.lunghezza_misurata}
              onChange={(e) => handleInputChange('lunghezza_misurata', e.target.value)}
              fullWidth
              inputProps={{ step: 0.01, min: 0 }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Valore Isolamento (MΩ)"
              value={formData.valore_isolamento}
              onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Valore Continuità"
              value={formData.valore_continuita}
              onChange={(e) => handleInputChange('valore_continuita', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Valore Resistenza"
              value={formData.valore_resistenza}
              onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}
              fullWidth
            />
          </Grid>

          {/* Note */}
          <Grid item xs={12}>
            <TextField
              label="Note"
              value={formData.note}
              onChange={(e) => handleInputChange('note', e.target.value)}
              fullWidth
              multiline
              rows={3}
            />
          </Grid>

          {/* Pulsanti */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={onCancel}
                disabled={loading}
                sx={{
                  borderColor: '#757575',
                  color: '#757575',
                  '&:hover': {
                    borderColor: '#424242',
                    backgroundColor: 'rgba(117, 117, 117, 0.04)'
                  }
                }}
              >
                Annulla
              </Button>
              <Button
                type="submit"
                variant="contained"
                startIcon={<SaveIcon />}
                disabled={loading}
                sx={{
                  backgroundColor: '#2196f3',
                  '&:hover': {
                    backgroundColor: '#1976d2'
                  }
                }}
              >
                {loading ? 'Salvataggio...' : 'Salva Certificazione'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
}

export default CertificazioneForm;

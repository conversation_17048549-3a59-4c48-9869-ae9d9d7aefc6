{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCaviImproved.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tabs, Tab, Pagination, InputAdornment, Divider, Stack, Chip, Tooltip, LinearProgress, Collapse, Snackbar, Container } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, Clear as ClearIcon, CheckCircle as CheckIcon, Warning as WarningIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Block as BlockIcon, Visibility as ViewIcon, PictureAsPdf as PdfIcon, Delete as DeleteIcon, Error as ErrorIcon, Cable as CableIcon, Build as BuildIcon, Link as LinkIcon, Download as DownloadIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport weatherService from '../../services/weatherService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviImproved = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError\n}, ref) => {\n  _s();\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('id_cavo');\n  const [sortOrder, setSortOrder] = useState('asc'); // Ordine crescente di default per ID cavo\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per dati meteorologici\n  const [weatherData, setWeatherData] = useState(null);\n  const [weatherLoading, setWeatherLoading] = useState(false);\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  // Riapplica filtri quando cambia il tab attivo E applica filtri iniziali\n  useEffect(() => {\n    if (activeTab === 0) {\n      filterCavi();\n    } else if (activeTab === 1) {\n      filterCertificazioni();\n    }\n  }, [activeTab, cavi, certificazioni]); // Aggiunto cavi e certificazioni come dipendenze\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      const caviData = await loadCavi();\n      setProgress(50);\n      const certificazioniData = await loadCertificazioni();\n      setProgress(75);\n      await loadStrumenti();\n      setProgress(100);\n\n      // Calcola le statistiche usando i dati appena caricati\n      // invece di aspettare che gli stati si aggiornino\n      calculateStatisticsWithData(caviData, certificazioniData);\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      // Ordina sempre i cavi per ID in ordine crescente (C001, C002, etc.)\n      const sortedData = data.sort((a, b) => {\n        // Estrai il numero dall'ID del cavo (es. C001 -> 1)\n        const getNumFromId = id => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        return getNumFromId(a.id_cavo) - getNumFromId(b.id_cavo);\n      });\n      setCavi(sortedData);\n      return sortedData;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Carica dati meteorologici per il cantiere\n  const loadWeatherData = async () => {\n    try {\n      setWeatherLoading(true);\n      const data = await weatherService.getFormattedWeatherForCantiere(cantiereId);\n      setWeatherData(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dati meteorologici:', error);\n      // Imposta dati demo in caso di errore\n      const demoData = {\n        temperature: 22.5,\n        humidity: 65,\n        displayText: 'Dati meteorologici non disponibili',\n        isDemo: true,\n        success: false\n      };\n      setWeatherData(demoData);\n      return demoData;\n    } finally {\n      setWeatherLoading(false);\n    }\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = useCallback(cavo => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  }, []);\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = useCallback(cavo => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  }, []);\n\n  // Calcola statistiche avanzate usando i dati passati come parametri\n  const calculateStatisticsWithData = useCallback((caviData, certificazioniData) => {\n    if (!caviData || !certificazioniData) {\n      console.log('Dati mancanti per il calcolo delle statistiche:', {\n        caviData: !!caviData,\n        certificazioniData: !!certificazioniData\n      });\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      cavi: caviData.length,\n      certificazioni: certificazioniData.length\n    });\n    const totaleCavi = caviData.length;\n    const caviCertificati = certificazioniData.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioniData.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioniData.filter(cert => new Date(cert.data_certificazione) >= unaSettimanaFa).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = caviData.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = caviData.filter(cavo => isCavoCollegato(cavo)).length;\n    const newStatistics = {\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  }, [puoEssereCertificato, isCavoCollegato]);\n\n  // Calcola statistiche avanzate usando gli stati correnti\n  const calculateStatistics = useCallback(() => {\n    if (!cavi || !certificazioni) {\n      console.log('Stati non pronti per il calcolo delle statistiche');\n      return;\n    }\n    calculateStatisticsWithData(cavi, certificazioni);\n  }, [cavi, certificazioni, calculateStatisticsWithData]);\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const closeSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo => {\n        var _cavo$tipologia, _cavo$ubicazione_part, _cavo$ubicazione_arri, _cavo$sezione, _cavo$utility;\n        return cavo.id_cavo.toLowerCase().includes(searchLower) || ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_part = cavo.ubicazione_partenza) === null || _cavo$ubicazione_part === void 0 ? void 0 : _cavo$ubicazione_part.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_arri = cavo.ubicazione_arrivo) === null || _cavo$ubicazione_arri === void 0 ? void 0 : _cavo$ubicazione_arri.toLowerCase().includes(searchLower)) || ((_cavo$sezione = cavo.sezione) === null || _cavo$sezione === void 0 ? void 0 : _cavo$sezione.toLowerCase().includes(searchLower)) || ((_cavo$utility = cavo.utility) === null || _cavo$utility === void 0 ? void 0 : _cavo$utility.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo => certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      }\n    }\n\n    // Ordinamento - speciale per ID cavo per mantenere ordine numerico\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Ordinamento speciale per ID cavo (C001, C002, etc.)\n      if (sortBy === 'id_cavo') {\n        const getNumFromId = id => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        const aNum = getNumFromId(aValue);\n        const bNum = getNumFromId(bValue);\n        if (sortOrder === 'asc') {\n          return aNum - bNum;\n        } else {\n          return bNum - aNum;\n        }\n      }\n\n      // Ordinamento normale per altri campi\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCavi(filtered);\n  };\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert => {\n        var _cert$operatore, _cert$numero_certific, _cert$note;\n        return cert.id_cavo.toLowerCase().includes(searchLower) || ((_cert$operatore = cert.operatore) === null || _cert$operatore === void 0 ? void 0 : _cert$operatore.toLowerCase().includes(searchLower)) || ((_cert$numero_certific = cert.numero_certificato) === null || _cert$numero_certific === void 0 ? void 0 : _cert$numero_certific.toLowerCase().includes(searchLower)) || ((_cert$note = cert.note) === null || _cert$note === void 0 ? void 0 : _cert$note.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) >= new Date(filters.dataInizio));\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) <= new Date(filters.dataFine));\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert => parseFloat(cert.valore_isolamento) >= valore);\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(!bulkMode ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle' : 'Modalità selezione disattivata', 'info');\n  };\n  const toggleItemSelection = itemId => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId];\n      showSnackbar(`${newSelection.length} certificazioni selezionate`, 'info');\n      return newSelection;\n    });\n  };\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = idCavo => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = cavo => {\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({\n      stato: '',\n      tipologia: '',\n      operatore: ''\n    });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = async (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Carica automaticamente i dati meteorologici\n    await loadWeatherData();\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n    setOpenDialog(true);\n  };\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleCavoSelect = cavo => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Funzione per collegare automaticamente un cavo\n  const collegaCavoAutomatico = async (cavoId, responsabile = 'cantiere') => {\n    try {\n      // Importa il servizio cavi\n      const caviService = (await import('../../services/caviService')).default;\n      let partenzaCollegata = false;\n      let arrivoCollegato = false;\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'partenza', responsabile);\n        partenzaCollegata = true;\n        console.log('Lato partenza collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          partenzaCollegata = true;\n          console.log('Lato partenza già collegato');\n        } else {\n          console.error('Errore nel collegamento lato partenza:', error);\n          throw error;\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile);\n        arrivoCollegato = true;\n        console.log('Lato arrivo collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          arrivoCollegato = true;\n          console.log('Lato arrivo già collegato');\n        } else {\n          console.error('Errore nel collegamento lato arrivo:', error);\n          throw error;\n        }\n      }\n      return partenzaCollegata && arrivoCollegato;\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per collegare un cavo direttamente dall'interfaccia di certificazione\n  const handleCollegaCavoFromCertification = async cavo => {\n    try {\n      const collegamenti = cavo.collegamenti || 0;\n      const statoCollegamenti = collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza collegata' : collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto';\n      const conferma = window.confirm(`Collegamento automatico del cavo ${cavo.id_cavo}\\n\\n` + `Stato attuale: ${statoCollegamenti}\\n\\n` + `Il sistema collegherà automaticamente entrambi i lati del cavo a \"cantiere\".\\n\\n` + `Vuoi procedere?`);\n      if (!conferma) {\n        return;\n      }\n      setOperationInProgress(true);\n      showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n      await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n      showSnackbar(`Cavo ${cavo.id_cavo} collegato automaticamente su entrambi i lati`, 'success');\n\n      // Ricarica i cavi per aggiornare lo stato nell'interfaccia\n      await loadCavi();\n    } catch (error) {\n      console.error('Errore nel collegamento del cavo:', error);\n      showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti collega automaticamente\n      if (!isCavoCollegato(cavo)) {\n        const statoCollegamenti = cavo.collegamenti === 0 ? 'Non collegato' : cavo.collegamenti === 1 ? 'Solo partenza collegata' : cavo.collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto';\n        const conferma = window.confirm(`ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` + `Stato collegamenti: ${statoCollegamenti}\\n\\n` + `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione?\\n\\n` + `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`);\n        if (!conferma) {\n          return;\n        }\n\n        // Collega automaticamente il cavo\n        try {\n          setOperationInProgress(true);\n          showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n          await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n          showSnackbar('Cavo collegato automaticamente su entrambi i lati', 'success');\n\n          // Ricarica i cavi per aggiornare lo stato\n          await loadCavi();\n        } catch (error) {\n          showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n          return;\n        }\n      }\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleGeneratePdf = async certificazione => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n      console.log(`🔍 DEBUG PDF - Generando PDF per certificazione:`, {\n        id_certificazione: certificazione.id_certificazione,\n        numero_certificato: certificazione.numero_certificato,\n        id_cavo: certificazione.id_cavo,\n        cantiere_id: cantiereId\n      });\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], {\n          type: 'application/pdf'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleDeleteCertificazione = async certificazione => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert => bulkSelection.includes(cert.id_certificazione));\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = data => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [cert.id_cavo, cert.numero_certificato, new Date(cert.data_certificazione).toLocaleDateString(), cert.operatore, cert.strumento, cert.lunghezza_misurata, cert.valore_isolamento, cert.risultato_finale]);\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: option => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    },\n    createCertificationForCavo: cavo => {\n      openCreateDialog(cavo);\n    },\n    viewCertificationForCavo: cavo => {\n      // Trova la certificazione per questo cavo\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        setSelectedItem(certificazione);\n        setDialogType('view');\n        setOpenDialog(true);\n      } else {\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    },\n    generatePdfForCavo: cavo => {\n      // Trova la certificazione per questo cavo e genera il PDF\n      console.log(`🔍 DEBUG PDF - Cercando certificazione per cavo ${cavo.id_cavo}`);\n      console.log(`🔍 DEBUG PDF - Certificazioni disponibili:`, certificazioni.map(c => ({\n        id: c.id_certificazione,\n        cavo: c.id_cavo\n      })));\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        console.log(`🔍 DEBUG PDF - Certificazione trovata:`, certificazione);\n        handleGeneratePdf(certificazione);\n      } else {\n        console.log(`❌ DEBUG PDF - Nessuna certificazione trovata per cavo ${cavo.id_cavo}`);\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = items => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n  const getTotalPages = items => Math.ceil(items.length / itemsPerPage);\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 989,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1001,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(BuildIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificabili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1016,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Pronti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1013,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' : statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.certificazioniOggi, \" oggi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1044,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 987,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 986,\n    columnNumber: 5\n  }, this);\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Cerca cavi, certificazioni, operatori...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 17\n            }, this),\n            endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setSearchTerm(''),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1074,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          onClick: () => setAdvancedFiltersOpen(!advancedFiltersOpen),\n          color: Object.values(filters).some(f => f) ? 'primary' : 'inherit',\n          children: [\"Filtri \", Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1083,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1082,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          onClick: toggleBulkMode,\n          color: bulkMode ? 'secondary' : 'inherit',\n          disabled: activeTab === 0,\n          children: bulkMode ? 'Esci Selezione' : 'Selezione Multipla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1094,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1093,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          onClick: handleExportAll,\n          disabled: activeTab === 0 || filteredCertificazioni.length === 0,\n          children: activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          onClick: openCreateDialog,\n          children: \"Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1058,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: advancedFiltersOpen,\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.stato,\n                onChange: e => setFilters({\n                  ...filters,\n                  stato: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"INSTALLATO\",\n                  children: \"Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1146,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_INSTALLATO\",\n                  children: \"Non Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"IN_CORSO\",\n                  children: \"In Corso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1139,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.tipologia,\n                onChange: e => setFilters({\n                  ...filters,\n                  tipologia: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1160,\n                  columnNumber: 21\n                }, this), [...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: tip,\n                  children: tip\n                }, tip, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.certificazione,\n                onChange: e => setFilters({\n                  ...filters,\n                  certificazione: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1175,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CERTIFICATO\",\n                  children: \"Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CERTIFICATO\",\n                  children: \"Non Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1168,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), activeTab === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.operatore,\n                onChange: e => setFilters({\n                  ...filters,\n                  operatore: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1194,\n                  columnNumber: 21\n                }, this), [...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op,\n                  children: op\n                }, op, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1196,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1190,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1188,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Test\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.risultatoTest,\n                onChange: e => setFilters({\n                  ...filters,\n                  risultatoTest: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1209,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: \"Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: \"Non Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: \"Da Verificare\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1212,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1203,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Isolamento Min (M\\u03A9)\",\n              type: \"number\",\n              value: filters.valoreIsolamento,\n              onChange: e => setFilters({\n                ...filters,\n                valoreIsolamento: e.target.value\n              }),\n              placeholder: \"es. 500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Inizio\",\n              type: \"date\",\n              value: filters.dataInizio,\n              onChange: e => setFilters({\n                ...filters,\n                dataInizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1230,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Fine\",\n              type: \"date\",\n              value: filters.dataFine,\n              onChange: e => setFilters({\n                ...filters,\n                dataFine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1241,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => setFilters({\n                stato: '',\n                tipologia: '',\n                operatore: '',\n                dataInizio: '',\n                dataFine: '',\n                valoreIsolamento: '',\n                risultatoTest: '',\n                strumento: '',\n                certificazione: ''\n              }),\n              children: \"Pulisci Tutti i Filtri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1257,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1134,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1128,\n      columnNumber: 7\n    }, this), bulkMode && bulkSelection.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [bulkSelection.length, \" elementi selezionati\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: selectAllItems,\n          children: \"Seleziona Tutto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: clearSelection,\n          children: \"Deseleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1288,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: handleBulkExport,\n          children: \"Esporta Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          color: \"error\",\n          onClick: handleBulkDelete,\n          children: \"Elimina Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1302,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1277,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1057,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n    if (filteredCavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.stato || filters.tipologia ? 'Nessun cavo trovato con i filtri applicati' : 'Nessun cavo disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1322,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1338,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('id_cavo');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'id_cavo' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1343,\n                      columnNumber: 70\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1343,\n                      columnNumber: 91\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1343,\n                      columnNumber: 113\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1339,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1337,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1353,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cavo => {\n              const isCertificato = isCavoCertificato(cavo.id_cavo);\n              const puoCertificare = puoEssereCertificato(cavo);\n              const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1367,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1373,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1374,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metratura_reale || cavo.metri_teorici, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1377,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1376,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: (() => {\n                    const collegamenti = cavo.collegamenti || 0;\n                    const statoCollegamento = collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza' : collegamenti === 2 ? 'Solo arrivo' : collegamenti === 3 ? 'Completo' : 'Sconosciuto';\n                    const colore = collegamenti === 3 ? 'success' : collegamenti === 0 ? 'error' : 'warning';\n                    return /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: statoCollegamento,\n                        color: colore,\n                        icon: collegamenti === 3 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1400,\n                          columnNumber: 58\n                        }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1400,\n                          columnNumber: 74\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1396,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1395,\n                      columnNumber: 27\n                    }, this);\n                  })()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1410,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Certificato\",\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1417,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Non certificato\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1415,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Cavo gi\\xE0 certificato\",\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1427,\n                        columnNumber: 35\n                      }, this),\n                      label: \"Certificato\",\n                      color: \"success\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1426,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1425,\n                    columnNumber: 25\n                  }, this) : puoCertificare ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Crea certificazione per questo cavo\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => openCreateDialog(cavo),\n                      color: \"primary\",\n                      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1440,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1435,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1434,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: messaggioErrore,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: true,\n                        onClick: () => showSnackbar(messaggioErrore, 'warning'),\n                        children: /*#__PURE__*/_jsxDEV(BlockIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1451,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1446,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1445,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1444,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1423,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1365,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1333,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1332,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCavi) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCavi),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1466,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1465,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n    if (filteredCertificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.operatore ? 'Nessuna certificazione trovata con i filtri applicati' : 'Nessuna certificazione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1484,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems,\n                  children: bulkSelection.length === filteredCertificazioni.length ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1504,\n                    columnNumber: 81\n                  }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1504,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1500,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1499,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"N\\xB0 Certificato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1510,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'numero_certificato' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1515,\n                      columnNumber: 81\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1515,\n                      columnNumber: 102\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1515,\n                      columnNumber: 124\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1511,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1509,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1519,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1522,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'data_certificazione' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1527,\n                      columnNumber: 82\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1527,\n                      columnNumber: 103\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1527,\n                      columnNumber: 125\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1523,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1521,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1520,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1531,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Strumento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1532,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lunghezza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1533,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Isolamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Risultato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1535,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1536,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1497,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1496,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n              selected: bulkSelection.includes(cert.id_certificazione),\n              hover: true,\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => toggleItemSelection(cert.id_certificazione),\n                  color: bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default',\n                  children: bulkSelection.includes(cert.id_certificazione) ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1553,\n                    columnNumber: 75\n                  }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1553,\n                    columnNumber: 91\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1548,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1547,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: cert.numero_certificato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1558,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.id_cavo,\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1563,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1562,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(cert.data_certificazione).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1565,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.operatore || cert.id_operatore\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1567,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1566,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.id_strumento ? (() => {\n                    const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                    return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                  })() : cert.strumento_utilizzato || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1570,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1569,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [cert.lunghezza_misurata, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1581,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1580,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: `${cert.valore_isolamento} MΩ`,\n                  color: parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning',\n                  icon: parseFloat(cert.valore_isolamento) >= 500 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1588,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1588,\n                    columnNumber: 89\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1584,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1583,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.risultato_finale || 'CONFORME',\n                  color: cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1592,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1591,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        setSelectedItem(cert);\n                        setDialogType('view');\n                        setOpenDialog(true);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1609,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1601,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1600,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Genera PDF\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleGeneratePdf(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1618,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1613,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1612,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteCertificazione(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1628,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1622,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1621,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1599,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1598,\n                columnNumber: 19\n              }, this)]\n            }, cert.id_certificazione, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1541,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1495,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1494,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCertificazioni) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCertificazioni),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1641,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1640,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1659,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: cavi.filter(cavo => {\n                // Mostra solo cavi che possono essere certificati o quello già selezionato\n                const isSelected = cavo.id_cavo === formData.id_cavo;\n                const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                const canBeCertified = puoEssereCertificato(cavo);\n                return isSelected || isNotCertified && canBeCertified;\n              }),\n              getOptionLabel: option => `${option.id_cavo} - ${option.tipologia}`,\n              value: cavi.find(c => c.id_cavo === formData.id_cavo) || null,\n              onChange: (event, newValue) => {\n                if (newValue) {\n                  handleCavoSelect(newValue);\n                } else {\n                  setFormData(prev => ({\n                    ...prev,\n                    id_cavo: '',\n                    lunghezza_misurata: ''\n                  }));\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Cavo *\",\n                placeholder: \"Seleziona un cavo posato\",\n                required: true,\n                helperText: \"Solo cavi posati/installati (il collegamento pu\\xF2 essere gestito al momento)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1684,\n                columnNumber: 19\n              }, this),\n              renderOption: (props, option) => {\n                const collegamenti = option.collegamenti || 0;\n                const isCollegato = collegamenti === 3;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"center\",\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"medium\",\n                          children: option.id_cavo\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1701,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [option.tipologia, \" - \", option.ubicazione_partenza, \" \\u2192 \", option.ubicazione_arrivo]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1704,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1700,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: option.stato_installazione,\n                          color: option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1709,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: isCollegato ? 'Collegato' : 'Da collegare',\n                          color: isCollegato ? 'success' : 'warning',\n                          icon: isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1718,\n                            columnNumber: 51\n                          }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1718,\n                            columnNumber: 67\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1714,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1708,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1699,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1698,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1697,\n                  columnNumber: 21\n                }, this);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1665,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1664,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Operatore *\",\n              value: formData.id_operatore,\n              onChange: e => handleFormChange('id_operatore', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1730,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1729,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Strumento *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1741,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.id_strumento,\n                onChange: e => handleFormChange('id_strumento', e.target.value),\n                label: \"Strumento *\",\n                children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: strumento.id_strumento,\n                  children: [strumento.nome, \" - \", strumento.marca, \" \", strumento.modello, \" (S/N: \", strumento.numero_serie, \")\"]\n                }, strumento.id_strumento, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1748,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1742,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1740,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1739,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Lunghezza Misurata (m) *\",\n              type: \"number\",\n              value: formData.lunghezza_misurata,\n              onChange: e => handleFormChange('lunghezza_misurata', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1757,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Continuit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1769,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_continuita,\n                onChange: e => handleFormChange('valore_continuita', e.target.value),\n                label: \"Continuit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1776,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1770,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1768,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Isolamento (M\\u03A9) *\",\n              type: \"number\",\n              value: formData.valore_isolamento,\n              onChange: e => handleFormChange('valore_isolamento', e.target.value),\n              required: true,\n              helperText: \"Valore minimo consigliato: 500 M\\u03A9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1782,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1781,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Resistenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1795,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_resistenza,\n                onChange: e => handleFormChange('valore_resistenza', e.target.value),\n                label: \"Resistenza\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1801,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1802,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1796,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1794,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1793,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato Collegamenti Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1810,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1809,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1808,\n            columnNumber: 13\n          }, this), formData.id_cavo && (() => {\n            const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n            if (!cavo) return null;\n            const collegamenti = cavo.collegamenti || 0;\n            const isCollegato = collegamenti === 3;\n            return /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 2,\n                  bgcolor: isCollegato ? 'success.light' : 'warning.light'\n                },\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 2,\n                  children: [isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1827,\n                    columnNumber: 38\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1827,\n                    columnNumber: 70\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1829,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [\"Stato: \", collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza collegata' : collegamenti === 2 ? 'Solo arrivo collegato' : collegamenti === 3 ? 'Completamente collegato' : 'Stato sconosciuto']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1832,\n                      columnNumber: 25\n                    }, this), !isCollegato && /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        mt: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        sx: {\n                          mb: 1\n                        },\n                        children: \"\\u26A0\\uFE0F Il cavo pu\\xF2 essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1841,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"medium\",\n                        variant: \"contained\",\n                        color: \"warning\",\n                        startIcon: /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1848,\n                          columnNumber: 42\n                        }, this),\n                        onClick: () => handleCollegaCavoFromCertification(cavo),\n                        disabled: operationInProgress,\n                        sx: {\n                          mt: 1,\n                          fontWeight: 'bold',\n                          textTransform: 'none',\n                          boxShadow: 3,\n                          '&:hover': {\n                            boxShadow: 6,\n                            transform: 'translateY(-1px)'\n                          },\n                          animation: 'pulse 2s infinite',\n                          '@keyframes pulse': {\n                            '0%': {\n                              boxShadow: '0 0 0 0 rgba(255, 152, 0, 0.7)'\n                            },\n                            '70%': {\n                              boxShadow: '0 0 0 10px rgba(255, 152, 0, 0)'\n                            },\n                            '100%': {\n                              boxShadow: '0 0 0 0 rgba(255, 152, 0, 0)'\n                            }\n                          }\n                        },\n                        children: \"\\uD83D\\uDD17 Collega Automaticamente\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1844,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1840,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1828,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1826,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1825,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1824,\n              columnNumber: 17\n            }, this);\n          })(), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Parametri Ambientali e Test Avanzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1888,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1887,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1886,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temperatura Ambiente (\\xB0C)\",\n              type: \"number\",\n              value: formData.temperatura_ambiente,\n              onChange: e => handleFormChange('temperatura_ambiente', e.target.value),\n              helperText: \"Temperatura durante il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1895,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1894,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Umidit\\xE0 (%)\",\n              type: \"number\",\n              value: formData.umidita,\n              onChange: e => handleFormChange('umidita', e.target.value),\n              helperText: \"Umidit\\xE0 relativa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1906,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1905,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Tensione di Prova (V)\",\n              type: \"number\",\n              value: formData.tensione_prova,\n              onChange: e => handleFormChange('tensione_prova', e.target.value),\n              helperText: \"Tensione applicata per il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1917,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1916,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Durata Prova (min)\",\n              type: \"number\",\n              value: formData.durata_prova,\n              onChange: e => handleFormChange('durata_prova', e.target.value),\n              helperText: \"Durata del test in minuti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1928,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1927,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Finale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1940,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.risultato_finale,\n                onChange: e => handleFormChange('risultato_finale', e.target.value),\n                label: \"Risultato Finale\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      color: \"success\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1948,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1949,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1947,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1946,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1954,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Non Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1955,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1953,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1952,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                      color: \"warning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1960,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Da Verificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1961,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1959,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1958,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1941,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1939,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1938,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              multiline: true,\n              rows: 3,\n              value: formData.note,\n              onChange: e => handleFormChange('note', e.target.value),\n              placeholder: \"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1969,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1968,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1663,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1662,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1982,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCertificazione,\n          variant: \"contained\",\n          disabled: loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento,\n          children: dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1983,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1981,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1658,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Certificazione - \", selectedItem.numero_certificato]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2001,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2009,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"ID Cavo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2013,\n                    columnNumber: 30\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2012,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Lunghezza Misurata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedItem.lunghezza_misurata, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2016,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2015,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2008,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2007,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2006,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2025,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Numero: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.numero_certificato\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2029,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2028,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Data: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: new Date(selectedItem.data_certificazione).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2032,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2031,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Operatore: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.operatore || selectedItem.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2035,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2034,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2024,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2023,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2022,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Risultati Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2044,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Continuit\\xE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2049,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_continuita,\n                      color: selectedItem.valore_continuita === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2052,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2048,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Isolamento\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2059,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: `${selectedItem.valore_isolamento} MΩ`,\n                      color: parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2062,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2058,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Resistenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2069,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_resistenza,\n                      color: selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2072,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2068,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2047,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2043,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2042,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2041,\n            columnNumber: 13\n          }, this), selectedItem.note && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2087,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedItem.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2090,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2086,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2085,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2084,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2005,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2004,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleGeneratePdf(selectedItem),\n          variant: \"contained\",\n          disabled: loading,\n          children: \"Genera PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2099,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2000,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 3\n    },\n    children: [renderDashboard(), (loading || operationInProgress) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2123,\n        columnNumber: 11\n      }, this), progress > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: [\"Caricamento... \", progress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2125,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2122,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"fullWidth\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Cavi da Certificare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2144,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [filteredCavi.length, \" cavi totali\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2147,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2143,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Certificazioni Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [filteredCertificazioni.length, \" certificazioni\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2155,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2133,\n      columnNumber: 7\n    }, this), renderSearchAndFilters(), !loading && activeTab === 0 && renderCaviTable(), !loading && activeTab === 1 && renderCertificazioniTable(), renderCertificazioneDialog(), renderViewDialog(), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: closeSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: closeSnackbar,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2180,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 2116,\n    columnNumber: 5\n  }, this);\n}, \"MjmGhNwgUwScHs3e3apudv/UXrM=\")), \"MjmGhNwgUwScHs3e3apudv/UXrM=\");\n_c2 = CertificazioneCaviImproved;\nexport default CertificazioneCaviImproved;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCaviImproved$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCaviImproved\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Tabs", "Tab", "Pagination", "InputAdornment", "Divider", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON><PERSON>", "LinearProgress", "Collapse", "Snackbar", "Container", "Add", "AddIcon", "Search", "SearchIcon", "Clear", "ClearIcon", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "Block", "BlockIcon", "Visibility", "ViewIcon", "PictureAsPdf", "PdfIcon", "Delete", "DeleteIcon", "Error", "ErrorIcon", "Cable", "CableIcon", "Build", "BuildIcon", "Link", "LinkIcon", "Download", "DownloadIcon", "certificazioneService", "caviService", "weatherService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CertificazioneCaviImproved", "_s", "_c", "cantiereId", "onSuccess", "onError", "ref", "loading", "setLoading", "activeTab", "setActiveTab", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "searchTerm", "setSearchTerm", "filteredCavi", "setFilteredCavi", "filteredCertificazioni", "setFilteredCertificazioni", "advancedFiltersOpen", "setAdvancedFiltersOpen", "filters", "setFilters", "stato", "tipologia", "operatore", "dataInizio", "dataFine", "valoreIsolamento", "risultatoTest", "strumento", "certificazione", "currentPage", "setCurrentPage", "itemsPerPage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedItem", "setSelectedItem", "bulkSelection", "setBulkSelection", "bulkMode", "setBulkMode", "snackbar", "setSnackbar", "open", "message", "severity", "progress", "setProgress", "operationInProgress", "setOperationInProgress", "formData", "setFormData", "id_cavo", "id_operatore", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "tensione_prova", "durata_prova", "risultato_finale", "weatherData", "setWeatherData", "weatherLoading", "setWeatherLoading", "statistics", "setStatistics", "totaleCavi", "caviCertificati", "caviNonCertificati", "percentualeCompletamento", "certificazioniOggi", "certificazioniSettimana", "loadInitialData", "filterCavi", "filterCertificazioni", "calculateStatistics", "caviData", "loadCavi", "certificazioniData", "loadCertificazioni", "loadStrumenti", "calculateStatisticsWithData", "error", "showSnackbar", "data", "getCertificazioni", "console", "get<PERSON><PERSON>", "sortedData", "sort", "a", "b", "getNumFromId", "id", "match", "parseInt", "getStrumenti", "loadWeatherData", "getFormattedWeatherForCantiere", "demoData", "temperature", "humidity", "displayText", "isDemo", "success", "puoEssereCertificato", "cavo", "isInstallato", "stato_installazione", "isCavoCollegato", "isCollegato", "colle<PERSON>nti", "hasResponsabili", "responsabile_partenza", "responsabile_arrivo", "log", "length", "Math", "round", "oggi", "Date", "toDateString", "filter", "cert", "data_certificazione", "unaSettimanaFa", "setDate", "getDate", "caviCertificabili", "caviNonCertificabili", "caviCollegati", "newStatistics", "closeSnackbar", "filtered", "searchLower", "toLowerCase", "_cavo$tipologia", "_cavo$ubicazione_part", "_cavo$ubicazione_arri", "_cavo$sezione", "_cavo$utility", "includes", "ubicazione_partenza", "ubicazione_arrivo", "sezione", "utility", "some", "aValue", "bValue", "aNum", "bNum", "_cert$operatore", "_cert$numero_certific", "_cert$note", "numero_certificato", "valore", "parseFloat", "toggleBulkMode", "toggleItemSelection", "itemId", "prev", "newSelection", "selectAllItems", "allIds", "map", "id_certificazione", "clearSelection", "isCavoCertificato", "idCavo", "getMessaggioErroreCertificazione", "handleTabChange", "event", "newValue", "openCreateDialog", "cavoPreselezionato", "metratura_reale", "metri_te<PERSON>ci", "closeDialog", "handleFormChange", "field", "value", "handleCavoSelect", "collegaCavoAutomatico", "cavoId", "responsabile", "default", "partenzaCollegata", "arrivoCollegato", "collegaCavo", "detail", "handleCollegaCavoFromCertification", "statoCollegamenti", "conferma", "window", "confirm", "handleCreateCertificazione", "find", "c", "messaggio", "createCertificazione", "handleGeneratePdf", "cantiere_id", "response", "generatePdf", "file_url", "newWindow", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "pdf_content", "blob", "Blob", "atob", "type", "url", "URL", "createObjectURL", "revokeObjectURL", "handleDeleteCertificazione", "deleteCertificazione", "handleBulkDelete", "handleBulkExport", "<PERSON><PERSON><PERSON><PERSON>", "csv<PERSON><PERSON>nt", "generateCSV", "downloadCSV", "toISOString", "split", "headers", "rows", "toLocaleDateString", "row", "join", "content", "filename", "undefined", "setAttribute", "style", "visibility", "handleExportAll", "handleOptionSelect", "option", "createCertificationForCavo", "viewCertificationForCavo", "generatePdfForCavo", "getCurrentPageItems", "items", "startIndex", "endIndex", "slice", "getTotalPages", "ceil", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderSearchAndFilters", "container", "item", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "endAdornment", "onClick", "size", "Object", "values", "f", "disabled", "in", "my", "Set", "Boolean", "tip", "op", "label", "InputLabelProps", "shrink", "renderCaviTable", "currentItems", "component", "isCertificato", "puoCertificare", "messaggioErrore", "statoCollegamento", "colore", "title", "icon", "mt", "count", "page", "renderCertificazioniTable", "padding", "selected", "hover", "s", "nome", "marca", "strumento_utilizzato", "renderCertificazioneDialog", "onClose", "max<PERSON><PERSON><PERSON>", "options", "isSelected", "isNotCertified", "canBeCertified", "getOptionLabel", "renderInput", "params", "required", "helperText", "renderOption", "props", "modello", "numero_serie", "startIcon", "textTransform", "boxShadow", "transform", "animation", "temperatura_ambiente", "<PERSON><PERSON><PERSON>", "multiline", "renderViewDialog", "gutterBottom", "py", "indicatorColor", "textColor", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tabs,\n  Tab,\n  Pagination,\n  InputAdornment,\n  Divider,\n  Stack,\n  Chip,\n  Tooltip,\n  LinearProgress,\n  Collapse,\n  Snackbar,\n  Container\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  Clear as ClearIcon,\n  CheckCircle as CheckIcon,\n  Warning as WarningIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  Block as BlockIcon,\n  Visibility as ViewIcon,\n  PictureAsPdf as PdfIcon,\n  Delete as DeleteIcon,\n  Error as ErrorIcon,\n  Cable as CableIcon,\n  Build as BuildIcon,\n  Link as LinkIcon,\n  Download as DownloadIcon\n} from '@mui/icons-material';\n\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport weatherService from '../../services/weatherService';\n\nconst CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('id_cavo');\n  const [sortOrder, setSortOrder] = useState('asc'); // Ordine crescente di default per ID cavo\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per dati meteorologici\n  const [weatherData, setWeatherData] = useState(null);\n  const [weatherLoading, setWeatherLoading] = useState(false);\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  // Riapplica filtri quando cambia il tab attivo E applica filtri iniziali\n  useEffect(() => {\n    if (activeTab === 0) {\n      filterCavi();\n    } else if (activeTab === 1) {\n      filterCertificazioni();\n    }\n  }, [activeTab, cavi, certificazioni]); // Aggiunto cavi e certificazioni come dipendenze\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      const caviData = await loadCavi();\n\n      setProgress(50);\n      const certificazioniData = await loadCertificazioni();\n\n      setProgress(75);\n      await loadStrumenti();\n\n      setProgress(100);\n\n      // Calcola le statistiche usando i dati appena caricati\n      // invece di aspettare che gli stati si aggiornino\n      calculateStatisticsWithData(caviData, certificazioniData);\n\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      // Ordina sempre i cavi per ID in ordine crescente (C001, C002, etc.)\n      const sortedData = data.sort((a, b) => {\n        // Estrai il numero dall'ID del cavo (es. C001 -> 1)\n        const getNumFromId = (id) => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        return getNumFromId(a.id_cavo) - getNumFromId(b.id_cavo);\n      });\n      setCavi(sortedData);\n      return sortedData;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Carica dati meteorologici per il cantiere\n  const loadWeatherData = async () => {\n    try {\n      setWeatherLoading(true);\n      const data = await weatherService.getFormattedWeatherForCantiere(cantiereId);\n      setWeatherData(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento dati meteorologici:', error);\n      // Imposta dati demo in caso di errore\n      const demoData = {\n        temperature: 22.5,\n        humidity: 65,\n        displayText: 'Dati meteorologici non disponibili',\n        isDemo: true,\n        success: false\n      };\n      setWeatherData(demoData);\n      return demoData;\n    } finally {\n      setWeatherLoading(false);\n    }\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = useCallback((cavo) => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  }, []);\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = useCallback((cavo) => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  }, []);\n\n  // Calcola statistiche avanzate usando i dati passati come parametri\n  const calculateStatisticsWithData = useCallback((caviData, certificazioniData) => {\n    if (!caviData || !certificazioniData) {\n      console.log('Dati mancanti per il calcolo delle statistiche:', { caviData: !!caviData, certificazioniData: !!certificazioniData });\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', { cavi: caviData.length, certificazioni: certificazioniData.length });\n\n    const totaleCavi = caviData.length;\n    const caviCertificati = certificazioniData.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioniData.filter(cert =>\n      new Date(cert.data_certificazione).toDateString() === oggi\n    ).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioniData.filter(cert =>\n      new Date(cert.data_certificazione) >= unaSettimanaFa\n    ).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = caviData.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = caviData.filter(cavo => isCavoCollegato(cavo)).length;\n\n    const newStatistics = {\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  }, [puoEssereCertificato, isCavoCollegato]);\n\n  // Calcola statistiche avanzate usando gli stati correnti\n  const calculateStatistics = useCallback(() => {\n    if (!cavi || !certificazioni) {\n      console.log('Stati non pronti per il calcolo delle statistiche');\n      return;\n    }\n    calculateStatisticsWithData(cavi, certificazioni);\n  }, [cavi, certificazioni, calculateStatisticsWithData]);\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({ open: true, message, severity });\n  };\n\n  const closeSnackbar = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(searchLower) ||\n        cavo.tipologia?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||\n        cavo.sezione?.toLowerCase().includes(searchLower) ||\n        cavo.utility?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      }\n    }\n\n    // Ordinamento - speciale per ID cavo per mantenere ordine numerico\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Ordinamento speciale per ID cavo (C001, C002, etc.)\n      if (sortBy === 'id_cavo') {\n        const getNumFromId = (id) => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        const aNum = getNumFromId(aValue);\n        const bNum = getNumFromId(bValue);\n\n        if (sortOrder === 'asc') {\n          return aNum - bNum;\n        } else {\n          return bNum - aNum;\n        }\n      }\n\n      // Ordinamento normale per altri campi\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCavi(filtered);\n  };\n\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert =>\n        cert.id_cavo.toLowerCase().includes(searchLower) ||\n        cert.operatore?.toLowerCase().includes(searchLower) ||\n        cert.numero_certificato?.toLowerCase().includes(searchLower) ||\n        cert.note?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)\n      );\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) <= new Date(filters.dataFine)\n      );\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert =>\n        parseFloat(cert.valore_isolamento) >= valore\n      );\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(\n      !bulkMode\n        ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle'\n        : 'Modalità selezione disattivata',\n      'info'\n    );\n  };\n\n  const toggleItemSelection = (itemId) => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId];\n\n      showSnackbar(\n        `${newSelection.length} certificazioni selezionate`,\n        'info'\n      );\n      return newSelection;\n    });\n  };\n\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = (idCavo) => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = (cavo) => {\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({ stato: '', tipologia: '', operatore: '' });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = async (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Carica automaticamente i dati meteorologici\n    await loadWeatherData();\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoSelect = (cavo) => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Funzione per collegare automaticamente un cavo\n  const collegaCavoAutomatico = async (cavoId, responsabile = 'cantiere') => {\n    try {\n      // Importa il servizio cavi\n      const caviService = (await import('../../services/caviService')).default;\n\n      let partenzaCollegata = false;\n      let arrivoCollegato = false;\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'partenza', responsabile);\n        partenzaCollegata = true;\n        console.log('Lato partenza collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          partenzaCollegata = true;\n          console.log('Lato partenza già collegato');\n        } else {\n          console.error('Errore nel collegamento lato partenza:', error);\n          throw error;\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile);\n        arrivoCollegato = true;\n        console.log('Lato arrivo collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          arrivoCollegato = true;\n          console.log('Lato arrivo già collegato');\n        } else {\n          console.error('Errore nel collegamento lato arrivo:', error);\n          throw error;\n        }\n      }\n\n      return partenzaCollegata && arrivoCollegato;\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per collegare un cavo direttamente dall'interfaccia di certificazione\n  const handleCollegaCavoFromCertification = async (cavo) => {\n    try {\n      const collegamenti = cavo.collegamenti || 0;\n      const statoCollegamenti = collegamenti === 0 ? 'Non collegato' :\n                               collegamenti === 1 ? 'Solo partenza collegata' :\n                               collegamenti === 2 ? 'Solo arrivo collegato' :\n                               'Stato sconosciuto';\n\n      const conferma = window.confirm(\n        `Collegamento automatico del cavo ${cavo.id_cavo}\\n\\n` +\n        `Stato attuale: ${statoCollegamenti}\\n\\n` +\n        `Il sistema collegherà automaticamente entrambi i lati del cavo a \"cantiere\".\\n\\n` +\n        `Vuoi procedere?`\n      );\n\n      if (!conferma) {\n        return;\n      }\n\n      setOperationInProgress(true);\n      showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n\n      await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n      showSnackbar(`Cavo ${cavo.id_cavo} collegato automaticamente su entrambi i lati`, 'success');\n\n      // Ricarica i cavi per aggiornare lo stato nell'interfaccia\n      await loadCavi();\n\n    } catch (error) {\n      console.error('Errore nel collegamento del cavo:', error);\n      showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti collega automaticamente\n      if (!isCavoCollegato(cavo)) {\n        const statoCollegamenti = cavo.collegamenti === 0 ? 'Non collegato' :\n                                 cavo.collegamenti === 1 ? 'Solo partenza collegata' :\n                                 cavo.collegamenti === 2 ? 'Solo arrivo collegato' :\n                                 'Stato sconosciuto';\n\n        const conferma = window.confirm(\n          `ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` +\n          `Stato collegamenti: ${statoCollegamenti}\\n\\n` +\n          `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione?\\n\\n` +\n          `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`\n        );\n\n        if (!conferma) {\n          return;\n        }\n\n        // Collega automaticamente il cavo\n        try {\n          setOperationInProgress(true);\n          showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n\n          await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n          showSnackbar('Cavo collegato automaticamente su entrambi i lati', 'success');\n\n          // Ricarica i cavi per aggiornare lo stato\n          await loadCavi();\n        } catch (error) {\n          showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n          return;\n        }\n      }\n\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleGeneratePdf = async (certificazione) => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n\n      console.log(`🔍 DEBUG PDF - Generando PDF per certificazione:`, {\n        id_certificazione: certificazione.id_certificazione,\n        numero_certificato: certificazione.numero_certificato,\n        id_cavo: certificazione.id_cavo,\n        cantiere_id: cantiereId\n      });\n\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], { type: 'application/pdf' });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleDeleteCertificazione = async (certificazione) => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert =>\n        bulkSelection.includes(cert.id_certificazione)\n      );\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = (data) => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [\n      cert.id_cavo,\n      cert.numero_certificato,\n      new Date(cert.data_certificazione).toLocaleDateString(),\n      cert.operatore,\n      cert.strumento,\n      cert.lunghezza_misurata,\n      cert.valore_isolamento,\n      cert.risultato_finale\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: (option) => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    },\n    createCertificationForCavo: (cavo) => {\n      openCreateDialog(cavo);\n    },\n    viewCertificationForCavo: (cavo) => {\n      // Trova la certificazione per questo cavo\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        setSelectedItem(certificazione);\n        setDialogType('view');\n        setOpenDialog(true);\n      } else {\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    },\n    generatePdfForCavo: (cavo) => {\n      // Trova la certificazione per questo cavo e genera il PDF\n      console.log(`🔍 DEBUG PDF - Cercando certificazione per cavo ${cavo.id_cavo}`);\n      console.log(`🔍 DEBUG PDF - Certificazioni disponibili:`, certificazioni.map(c => ({ id: c.id_certificazione, cavo: c.id_cavo })));\n\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        console.log(`🔍 DEBUG PDF - Certificazione trovata:`, certificazione);\n        handleGeneratePdf(certificazione);\n      } else {\n        console.log(`❌ DEBUG PDF - Nessuna certificazione trovata per cavo ${cavo.id_cavo}`);\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = (items) => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n\n  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);\n\n\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <BuildIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificabili}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Pronti\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' :\n                     statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.percentualeCompletamento}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              Completamento\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.certificazioniOggi} oggi\n            </Typography>\n          </Box>\n        </Stack>\n\n\n      </Stack>\n    </Paper>\n  );\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => (\n    <Paper sx={{ p: 2, mb: 3 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            placeholder=\"Cerca cavi, certificazioni, operatori...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n              endAdornment: searchTerm && (\n                <InputAdornment position=\"end\">\n                  <IconButton onClick={() => setSearchTerm('')} size=\"small\">\n                    <ClearIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}\n            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}\n          >\n            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            onClick={toggleBulkMode}\n            color={bulkMode ? 'secondary' : 'inherit'}\n            disabled={activeTab === 0}\n          >\n            {bulkMode ? 'Esci Selezione' : 'Selezione Multipla'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            onClick={handleExportAll}\n            disabled={activeTab === 0 || filteredCertificazioni.length === 0}\n          >\n            {activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            onClick={openCreateDialog}\n          >\n            Nuova Certificazione\n          </Button>\n        </Grid>\n      </Grid>\n\n      {/* Filtri avanzati - Diversi per ogni tab */}\n      <Collapse in={advancedFiltersOpen}>\n        <Divider sx={{ my: 2 }} />\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'}\n        </Typography>\n\n        <Grid container spacing={2}>\n          {/* Filtri per tab Cavi */}\n          {activeTab === 0 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    value={filters.stato}\n                    onChange={(e) => setFilters({...filters, stato: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"INSTALLATO\">Installato</MenuItem>\n                    <MenuItem value=\"NON_INSTALLATO\">Non Installato</MenuItem>\n                    <MenuItem value=\"IN_CORSO\">In Corso</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Tipologia</InputLabel>\n                  <Select\n                    value={filters.tipologia}\n                    onChange={(e) => setFilters({...filters, tipologia: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutte</MenuItem>\n                    {[...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => (\n                      <MenuItem key={tip} value={tip}>{tip}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Certificazione</InputLabel>\n                  <Select\n                    value={filters.certificazione}\n                    onChange={(e) => setFilters({...filters, certificazione: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CERTIFICATO\">Certificato</MenuItem>\n                    <MenuItem value=\"NON_CERTIFICATO\">Non Certificato</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </>\n          )}\n\n          {/* Filtri per tab Certificazioni */}\n          {activeTab === 1 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Operatore</InputLabel>\n                  <Select\n                    value={filters.operatore}\n                    onChange={(e) => setFilters({...filters, operatore: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    {[...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => (\n                      <MenuItem key={op} value={op}>{op}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Risultato Test</InputLabel>\n                  <Select\n                    value={filters.risultatoTest}\n                    onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CONFORME\">Conforme</MenuItem>\n                    <MenuItem value=\"NON_CONFORME\">Non Conforme</MenuItem>\n                    <MenuItem value=\"DA_VERIFICARE\">Da Verificare</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Isolamento Min (MΩ)\"\n                  type=\"number\"\n                  value={filters.valoreIsolamento}\n                  onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}\n                  placeholder=\"es. 500\"\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Inizio\"\n                  type=\"date\"\n                  value={filters.dataInizio}\n                  onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Fine\"\n                  type=\"date\"\n                  value={filters.dataFine}\n                  onChange={(e) => setFilters({...filters, dataFine: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n\n          <Grid item xs={12}>\n            <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => setFilters({\n                  stato: '', tipologia: '', operatore: '', dataInizio: '',\n                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: '',\n                  certificazione: ''\n                })}\n              >\n                Pulisci Tutti i Filtri\n              </Button>\n            </Stack>\n          </Grid>\n        </Grid>\n      </Collapse>\n\n      {/* Barra azioni bulk */}\n      {bulkMode && bulkSelection.length > 0 && (\n        <>\n          <Divider sx={{ my: 2 }} />\n          <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n            <Typography variant=\"body2\">\n              {bulkSelection.length} elementi selezionati\n            </Typography>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={selectAllItems}\n            >\n              Seleziona Tutto\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={clearSelection}\n            >\n              Deseleziona\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={handleBulkExport}\n            >\n              Esporta Selezionati\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              color=\"error\"\n              onClick={handleBulkDelete}\n            >\n              Elimina Selezionati\n            </Button>\n          </Stack>\n        </>\n      )}\n    </Paper>\n  );\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n\n    if (filteredCavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.stato || filters.tipologia\n            ? 'Nessun cavo trovato con i filtri applicati'\n            : 'Nessun cavo disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">ID Cavo</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('id_cavo');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'id_cavo' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Partenza</TableCell>\n                <TableCell>Arrivo</TableCell>\n                <TableCell>Metri</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n                <TableCell>Certificato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cavo) => {\n                const isCertificato = isCavoCertificato(cavo.id_cavo);\n                const puoCertificare = puoEssereCertificato(cavo);\n                const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia}</TableCell>\n                    <TableCell>{cavo.sezione}</TableCell>\n                    <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label={cavo.stato_installazione}\n                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {(() => {\n                        const collegamenti = cavo.collegamenti || 0;\n                        const statoCollegamento = collegamenti === 0 ? 'Non collegato' :\n                                                 collegamenti === 1 ? 'Solo partenza' :\n                                                 collegamenti === 2 ? 'Solo arrivo' :\n                                                 collegamenti === 3 ? 'Completo' :\n                                                 'Sconosciuto';\n                        const colore = collegamenti === 3 ? 'success' :\n                                      collegamenti === 0 ? 'error' : 'warning';\n\n                        return (\n                          <Tooltip title={`Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`}>\n                            <Chip\n                              size=\"small\"\n                              label={statoCollegamento}\n                              color={colore}\n                              icon={collegamenti === 3 ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Tooltip>\n                        );\n                      })()}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Chip\n                          size=\"small\"\n                          icon={<CheckIcon />}\n                          label=\"Certificato\"\n                          color=\"success\"\n                        />\n                      ) : (\n                        <Chip\n                          size=\"small\"\n                          icon={<WarningIcon />}\n                          label=\"Non certificato\"\n                          color=\"warning\"\n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Tooltip title=\"Cavo già certificato\">\n                          <Chip\n                            icon={<CheckIcon />}\n                            label=\"Certificato\"\n                            color=\"success\"\n                            size=\"small\"\n                          />\n                        </Tooltip>\n                      ) : puoCertificare ? (\n                        <Tooltip title=\"Crea certificazione per questo cavo\">\n                          <IconButton\n                            size=\"small\"\n                            onClick={() => openCreateDialog(cavo)}\n                            color=\"primary\"\n                          >\n                            <AddIcon />\n                          </IconButton>\n                        </Tooltip>\n                      ) : (\n                        <Tooltip title={messaggioErrore}>\n                          <span>\n                            <IconButton\n                              size=\"small\"\n                              disabled\n                              onClick={() => showSnackbar(messaggioErrore, 'warning')}\n                            >\n                              <BlockIcon />\n                            </IconButton>\n                          </span>\n                        </Tooltip>\n                      )}\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCavi) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCavi)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n\n    if (filteredCertificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.operatore\n            ? 'Nessuna certificazione trovata con i filtri applicati'\n            : 'Nessuna certificazione disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                {bulkMode && (\n                  <TableCell padding=\"checkbox\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}\n                    >\n                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}\n                    </IconButton>\n                  </TableCell>\n                )}\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">N° Certificato</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">Data</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Operatore</TableCell>\n                <TableCell>Strumento</TableCell>\n                <TableCell>Lunghezza</TableCell>\n                <TableCell>Isolamento</TableCell>\n                <TableCell>Risultato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cert) => (\n                <TableRow\n                  key={cert.id_certificazione}\n                  selected={bulkSelection.includes(cert.id_certificazione)}\n                  hover\n                >\n                  {bulkMode && (\n                    <TableCell padding=\"checkbox\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => toggleItemSelection(cert.id_certificazione)}\n                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}\n                      >\n                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}\n                      </IconButton>\n                    </TableCell>\n                  )}\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                      {cert.numero_certificato}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip size=\"small\" label={cert.id_cavo} variant=\"outlined\" />\n                  </TableCell>\n                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.operatore || cert.id_operatore}</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">\n                      {cert.id_strumento ?\n                        (() => {\n                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                        })()\n                        : (cert.strumento_utilizzato || 'N/A')\n                      }\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.lunghezza_misurata} m</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={`${cert.valore_isolamento} MΩ`}\n                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={cert.risultato_finale || 'CONFORME'}\n                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" spacing={0.5}>\n                      <Tooltip title=\"Visualizza dettagli\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            setSelectedItem(cert);\n                            setDialogType('view');\n                            setOpenDialog(true);\n                          }}\n                        >\n                          <ViewIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Genera PDF\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleGeneratePdf(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <PdfIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleDeleteCertificazione(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCertificazioni) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCertificazioni)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Autocomplete\n                options={cavi.filter(cavo => {\n                  // Mostra solo cavi che possono essere certificati o quello già selezionato\n                  const isSelected = cavo.id_cavo === formData.id_cavo;\n                  const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                  const canBeCertified = puoEssereCertificato(cavo);\n\n                  return isSelected || (isNotCertified && canBeCertified);\n                })}\n                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}\n                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}\n                onChange={(event, newValue) => {\n                  if (newValue) {\n                    handleCavoSelect(newValue);\n                  } else {\n                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));\n                  }\n                }}\n                renderInput={(params) => (\n                  <TextField\n                    {...params}\n                    label=\"Cavo *\"\n                    placeholder=\"Seleziona un cavo posato\"\n                    required\n                    helperText=\"Solo cavi posati/installati (il collegamento può essere gestito al momento)\"\n                  />\n                )}\n                renderOption={(props, option) => {\n                  const collegamenti = option.collegamenti || 0;\n                  const isCollegato = collegamenti === 3;\n\n                  return (\n                    <Box component=\"li\" {...props}>\n                      <Box sx={{ width: '100%' }}>\n                        <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"center\">\n                          <Box>\n                            <Typography variant=\"body2\" fontWeight=\"medium\">\n                              {option.id_cavo}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}\n                            </Typography>\n                          </Box>\n                          <Stack direction=\"row\" spacing={1}>\n                            <Chip\n                              size=\"small\"\n                              label={option.stato_installazione}\n                              color={option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                            />\n                            <Chip\n                              size=\"small\"\n                              label={isCollegato ? 'Collegato' : 'Da collegare'}\n                              color={isCollegato ? 'success' : 'warning'}\n                              icon={isCollegato ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Stack>\n                        </Stack>\n                      </Box>\n                    </Box>\n                  );\n                }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Operatore *\"\n                value={formData.id_operatore}\n                onChange={(e) => handleFormChange('id_operatore', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required>\n                <InputLabel>Strumento *</InputLabel>\n                <Select\n                  value={formData.id_strumento}\n                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}\n                  label=\"Strumento *\"\n                >\n                  {strumenti.map((strumento) => (\n                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Lunghezza Misurata (m) *\"\n                type=\"number\"\n                value={formData.lunghezza_misurata}\n                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Continuità</InputLabel>\n                <Select\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}\n                  label=\"Continuità\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Isolamento (MΩ) *\"\n                type=\"number\"\n                value={formData.valore_isolamento}\n                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}\n                required\n                helperText=\"Valore minimo consigliato: 500 MΩ\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Resistenza</InputLabel>\n                <Select\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}\n                  label=\"Resistenza\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            {/* Sezione Collegamenti */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato Collegamenti Cavo\n                </Typography>\n              </Divider>\n            </Grid>\n\n            {formData.id_cavo && (() => {\n              const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n              if (!cavo) return null;\n\n              const collegamenti = cavo.collegamenti || 0;\n              const isCollegato = collegamenti === 3;\n\n              return (\n                <Grid item xs={12}>\n                  <Paper sx={{ p: 2, bgcolor: isCollegato ? 'success.light' : 'warning.light' }}>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                      {isCollegato ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />}\n                      <Box>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'}\n                        </Typography>\n                        <Typography variant=\"caption\">\n                          Stato: {collegamenti === 0 ? 'Non collegato' :\n                                  collegamenti === 1 ? 'Solo partenza collegata' :\n                                  collegamenti === 2 ? 'Solo arrivo collegato' :\n                                  collegamenti === 3 ? 'Completamente collegato' :\n                                  'Stato sconosciuto'}\n                        </Typography>\n                        {!isCollegato && (\n                          <Box sx={{ mt: 1 }}>\n                            <Typography variant=\"caption\" display=\"block\" sx={{ mb: 1 }}>\n                              ⚠️ Il cavo può essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\n                            </Typography>\n                            <Button\n                              size=\"medium\"\n                              variant=\"contained\"\n                              color=\"warning\"\n                              startIcon={<LinkIcon />}\n                              onClick={() => handleCollegaCavoFromCertification(cavo)}\n                              disabled={operationInProgress}\n                              sx={{\n                                mt: 1,\n                                fontWeight: 'bold',\n                                textTransform: 'none',\n                                boxShadow: 3,\n                                '&:hover': {\n                                  boxShadow: 6,\n                                  transform: 'translateY(-1px)'\n                                },\n                                animation: 'pulse 2s infinite',\n                                '@keyframes pulse': {\n                                  '0%': {\n                                    boxShadow: '0 0 0 0 rgba(255, 152, 0, 0.7)'\n                                  },\n                                  '70%': {\n                                    boxShadow: '0 0 0 10px rgba(255, 152, 0, 0)'\n                                  },\n                                  '100%': {\n                                    boxShadow: '0 0 0 0 rgba(255, 152, 0, 0)'\n                                  }\n                                }\n                              }}\n                            >\n                              🔗 Collega Automaticamente\n                            </Button>\n                          </Box>\n                        )}\n                      </Box>\n                    </Stack>\n                  </Paper>\n                </Grid>\n              );\n            })()}\n\n            {/* Campi avanzati */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Parametri Ambientali e Test Avanzati\n                </Typography>\n              </Divider>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Temperatura Ambiente (°C)\"\n                type=\"number\"\n                value={formData.temperatura_ambiente}\n                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}\n                helperText=\"Temperatura durante il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Umidità (%)\"\n                type=\"number\"\n                value={formData.umidita}\n                onChange={(e) => handleFormChange('umidita', e.target.value)}\n                helperText=\"Umidità relativa\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Tensione di Prova (V)\"\n                type=\"number\"\n                value={formData.tensione_prova}\n                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}\n                helperText=\"Tensione applicata per il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Durata Prova (min)\"\n                type=\"number\"\n                value={formData.durata_prova}\n                onChange={(e) => handleFormChange('durata_prova', e.target.value)}\n                helperText=\"Durata del test in minuti\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Risultato Finale</InputLabel>\n                <Select\n                  value={formData.risultato_finale}\n                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}\n                  label=\"Risultato Finale\"\n                >\n                  <MenuItem value=\"CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <CheckIcon color=\"success\" />\n                      <Typography>Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"NON_CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <ErrorIcon color=\"error\" />\n                      <Typography>Non Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"DA_VERIFICARE\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <WarningIcon color=\"warning\" />\n                      <Typography>Da Verificare</Typography>\n                    </Stack>\n                  </MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                multiline\n                rows={3}\n                value={formData.note}\n                onChange={(e) => handleFormChange('note', e.target.value)}\n                placeholder=\"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Annulla</Button>\n          <Button\n            onClick={handleCreateCertificazione}\n            variant=\"contained\"\n            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}\n          >\n            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Certificazione - {selectedItem.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Cavo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Certificazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Numero: <strong>{selectedItem.numero_certificato}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Risultati Test\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Continuità\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_continuita}\n                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Isolamento\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={`${selectedItem.valore_isolamento} MΩ`}\n                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Resistenza\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_resistenza}\n                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                  </Grid>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {selectedItem.note && (\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Note\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {selectedItem.note}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            )}\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Chiudi</Button>\n          <Button\n            onClick={() => handleGeneratePdf(selectedItem)}\n            variant=\"contained\"\n            disabled={loading}\n          >\n            Genera PDF\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      {/* Dashboard con statistiche */}\n      {renderDashboard()}\n\n      {/* Progress bar per operazioni in corso */}\n      {(loading || operationInProgress) && (\n        <Box sx={{ mb: 2 }}>\n          <LinearProgress />\n          {progress > 0 && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n              Caricamento... {progress}%\n            </Typography>\n          )}\n        </Box>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"fullWidth\"\n        >\n          <Tab\n            label={\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"bold\">\n                  Cavi da Certificare\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {filteredCavi.length} cavi totali\n                </Typography>\n              </Box>\n            }\n          />\n          <Tab\n            label={\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"bold\">\n                  Certificazioni Completate\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {filteredCertificazioni.length} certificazioni\n                </Typography>\n              </Box>\n            }\n          />\n        </Tabs>\n      </Paper>\n\n      {/* Barra di ricerca e filtri avanzati */}\n      {renderSearchAndFilters()}\n\n      {/* Contenuto delle tabs */}\n      {!loading && activeTab === 0 && renderCaviTable()}\n      {!loading && activeTab === 1 && renderCertificazioniTable()}\n\n      {/* Dialogs */}\n      {renderCertificazioneDialog()}\n      {renderViewDialog()}\n\n      {/* Snackbar per notifiche */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={closeSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n\n\n    </Container>\n  );\n});\n\nexport default CertificazioneCaviImproved;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAChG,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,QAAQ,EACRC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,QAAQ,EACtBC,YAAY,IAAIC,OAAO,EACvBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,0BAA0B,gBAAAC,EAAA,cAAGhF,UAAU,CAAAiF,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2F,SAAS,EAAEC,YAAY,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC6F,cAAc,EAAEC,iBAAiB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+F,IAAI,EAAEC,OAAO,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiG,SAAS,EAAEC,YAAY,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACmG,UAAU,EAAEC,aAAa,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqG,YAAY,EAAEC,eAAe,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACyG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1G,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC2G,OAAO,EAAEC,UAAU,CAAC,GAAG5G,QAAQ,CAAC;IACrC6G,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwH,YAAY,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACyH,MAAM,EAAEC,SAAS,CAAC,GAAG1H,QAAQ,CAAC,SAAS,CAAC;EAC/C,MAAM,CAAC2H,SAAS,EAAEC,YAAY,CAAC,GAAG5H,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM,CAAC6H,UAAU,EAAEC,aAAa,CAAC,GAAG9H,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+H,UAAU,EAAEC,aAAa,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiI,YAAY,EAAEC,eAAe,CAAC,GAAGlI,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmI,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqI,QAAQ,EAAEC,WAAW,CAAC,GAAGtI,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAACuI,QAAQ,EAAEC,WAAW,CAAC,GAAGxI,QAAQ,CAAC;IAAEyI,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG7I,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC8I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/I,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACgJ,QAAQ,EAAEC,WAAW,CAAC,GAAGjJ,QAAQ,CAAC;IACvCkJ,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9J,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+J,cAAc,EAAEC,iBAAiB,CAAC,GAAGhK,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACiK,UAAU,EAAEC,aAAa,CAAC,GAAGlK,QAAQ,CAAC;IAC3CmK,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACAvK,SAAS,CAAC,MAAM;IACdwK,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACpF,UAAU,CAAC,CAAC;;EAEhB;EACApF,SAAS,CAAC,MAAM;IACdyK,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC3E,IAAI,EAAEI,UAAU,EAAEQ,OAAO,EAAEc,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAElD;EACA1H,SAAS,CAAC,MAAM;IACd0K,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC9E,cAAc,EAAEM,UAAU,EAAEQ,OAAO,EAAEc,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAE5D;EACA1H,SAAS,CAAC,MAAM;IACd2K,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC7E,IAAI,EAAEF,cAAc,CAAC,CAAC;;EAE1B;EACA5F,SAAS,CAAC,MAAM;IACd,IAAI0F,SAAS,KAAK,CAAC,EAAE;MACnB+E,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAI/E,SAAS,KAAK,CAAC,EAAE;MAC1BgF,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAChF,SAAS,EAAEI,IAAI,EAAEF,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEvC,MAAM4E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF/E,UAAU,CAAC,IAAI,CAAC;MAChBmD,WAAW,CAAC,CAAC,CAAC;;MAEd;MACAA,WAAW,CAAC,EAAE,CAAC;MACf,MAAMgC,QAAQ,GAAG,MAAMC,QAAQ,CAAC,CAAC;MAEjCjC,WAAW,CAAC,EAAE,CAAC;MACf,MAAMkC,kBAAkB,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAErDnC,WAAW,CAAC,EAAE,CAAC;MACf,MAAMoC,aAAa,CAAC,CAAC;MAErBpC,WAAW,CAAC,GAAG,CAAC;;MAEhB;MACA;MACAqC,2BAA2B,CAACL,QAAQ,EAAEE,kBAAkB,CAAC;IAE3D,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,0CAA0C,EAAE,OAAO,CAAC;MACjE7F,OAAO,CAAC,0CAA0C,CAAC;IACrD,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;MACjBmD,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMmC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMK,IAAI,GAAG,MAAM1G,qBAAqB,CAAC2G,iBAAiB,CAACjG,UAAU,CAAC;MACtES,iBAAiB,CAACuF,IAAI,CAAC;MACvB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAML,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMO,IAAI,GAAG,MAAMzG,WAAW,CAAC4G,OAAO,CAACnG,UAAU,CAAC;MAClD;MACA,MAAMoG,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACrC;QACA,MAAMC,YAAY,GAAIC,EAAE,IAAK;UAC3B,MAAMC,KAAK,GAAGD,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;UAC/B,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC3C,CAAC;QACD,OAAOF,YAAY,CAACF,CAAC,CAACzC,OAAO,CAAC,GAAG2C,YAAY,CAACD,CAAC,CAAC1C,OAAO,CAAC;MAC1D,CAAC,CAAC;MACFlD,OAAO,CAACyF,UAAU,CAAC;MACnB,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAON,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMI,IAAI,GAAG,MAAM1G,qBAAqB,CAACsH,YAAY,CAAC5G,UAAU,CAAC;MACjEa,YAAY,CAACmF,IAAI,CAAC;MAClB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFlC,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMqB,IAAI,GAAG,MAAMxG,cAAc,CAACsH,8BAA8B,CAAC9G,UAAU,CAAC;MAC5EyE,cAAc,CAACuB,IAAI,CAAC;MACpB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE;MACA,MAAMiB,QAAQ,GAAG;QACfC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,oCAAoC;QACjDC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE;MACX,CAAC;MACD3C,cAAc,CAACsC,QAAQ,CAAC;MACxB,OAAOA,QAAQ;IACjB,CAAC,SAAS;MACRpC,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAM0C,oBAAoB,GAAGxM,WAAW,CAAEyM,IAAI,IAAK;IACjD;IACA,MAAMC,YAAY,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAC1CF,IAAI,CAACE,mBAAmB,KAAK,YAAY,IACzCF,IAAI,CAACE,mBAAmB,KAAK,QAAQ;;IAEzD;IACA;IACA,OAAOD,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,eAAe,GAAG5M,WAAW,CAAEyM,IAAI,IAAK;IAC5C,MAAMI,WAAW,GAAGJ,IAAI,CAACK,YAAY,KAAK,CAAC;IAC3C,MAAMC,eAAe,GAAGN,IAAI,CAACO,qBAAqB,IAAIP,IAAI,CAACQ,mBAAmB;IAC9E,OAAOJ,WAAW,IAAIE,eAAe;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM/B,2BAA2B,GAAGhL,WAAW,CAAC,CAAC2K,QAAQ,EAAEE,kBAAkB,KAAK;IAChF,IAAI,CAACF,QAAQ,IAAI,CAACE,kBAAkB,EAAE;MACpCQ,OAAO,CAAC6B,GAAG,CAAC,iDAAiD,EAAE;QAAEvC,QAAQ,EAAE,CAAC,CAACA,QAAQ;QAAEE,kBAAkB,EAAE,CAAC,CAACA;MAAmB,CAAC,CAAC;MAClI;IACF;IAEAQ,OAAO,CAAC6B,GAAG,CAAC,+BAA+B,EAAE;MAAErH,IAAI,EAAE8E,QAAQ,CAACwC,MAAM;MAAExH,cAAc,EAAEkF,kBAAkB,CAACsC;IAAO,CAAC,CAAC;IAElH,MAAMlD,UAAU,GAAGU,QAAQ,CAACwC,MAAM;IAClC,MAAMjD,eAAe,GAAGW,kBAAkB,CAACsC,MAAM;IACjD,MAAMhD,kBAAkB,GAAGF,UAAU,GAAGC,eAAe;IACvD,MAAME,wBAAwB,GAAGH,UAAU,GAAG,CAAC,GAAGmD,IAAI,CAACC,KAAK,CAAEnD,eAAe,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEtG;IACA,MAAMqD,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACtC,MAAMnD,kBAAkB,GAAGQ,kBAAkB,CAAC4C,MAAM,CAACC,IAAI,IACvD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACH,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACH,MAAM;;IAER;IACA,MAAMS,cAAc,GAAG,IAAIL,IAAI,CAAC,CAAC;IACjCK,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,MAAMxD,uBAAuB,GAAGO,kBAAkB,CAAC4C,MAAM,CAACC,IAAI,IAC5D,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAIC,cACxC,CAAC,CAACT,MAAM;;IAER;IACA,MAAMY,iBAAiB,GAAGpD,QAAQ,CAAC8C,MAAM,CAAChB,IAAI,IAAID,oBAAoB,CAACC,IAAI,CAAC,CAAC,CAACU,MAAM;IACpF,MAAMa,oBAAoB,GAAG/D,UAAU,GAAG8D,iBAAiB;;IAE3D;IACA,MAAME,aAAa,GAAGtD,QAAQ,CAAC8C,MAAM,CAAChB,IAAI,IAAIG,eAAe,CAACH,IAAI,CAAC,CAAC,CAACU,MAAM;IAE3E,MAAMe,aAAa,GAAG;MACpBjE,UAAU;MACVC,eAAe;MACfC,kBAAkB;MAClB4D,iBAAiB;MACjBC,oBAAoB;MACpBC,aAAa;MACb7D,wBAAwB;MACxBC,kBAAkB;MAClBC;IACF,CAAC;IAEDe,OAAO,CAAC6B,GAAG,CAAC,8BAA8B,EAAEgB,aAAa,CAAC;IAC1DlE,aAAa,CAACkE,aAAa,CAAC;EAC9B,CAAC,EAAE,CAAC1B,oBAAoB,EAAEI,eAAe,CAAC,CAAC;;EAE3C;EACA,MAAMlC,mBAAmB,GAAG1K,WAAW,CAAC,MAAM;IAC5C,IAAI,CAAC6F,IAAI,IAAI,CAACF,cAAc,EAAE;MAC5B0F,OAAO,CAAC6B,GAAG,CAAC,mDAAmD,CAAC;MAChE;IACF;IACAlC,2BAA2B,CAACnF,IAAI,EAAEF,cAAc,CAAC;EACnD,CAAC,EAAE,CAACE,IAAI,EAAEF,cAAc,EAAEqF,2BAA2B,CAAC,CAAC;;EAEvD;EACA,MAAME,YAAY,GAAGA,CAAC1C,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACnDH,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EAChD,CAAC;EAED,MAAM0F,aAAa,GAAGA,CAAA,KAAM;IAC1B7F,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMiC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI4D,QAAQ,GAAGvI,IAAI;;IAEnB;IACA,IAAII,UAAU,EAAE;MACd,MAAMoI,WAAW,GAAGpI,UAAU,CAACqI,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI;QAAA,IAAA8B,eAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAC7BlC,IAAI,CAACzD,OAAO,CAACsF,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAE,eAAA,GAChD9B,IAAI,CAAC7F,SAAS,cAAA2H,eAAA,uBAAdA,eAAA,CAAgBD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAG,qBAAA,GACnD/B,IAAI,CAACoC,mBAAmB,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BF,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAI,qBAAA,GAC7DhC,IAAI,CAACqC,iBAAiB,cAAAL,qBAAA,uBAAtBA,qBAAA,CAAwBH,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAK,aAAA,GAC3DjC,IAAI,CAACsC,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcJ,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAM,aAAA,GACjDlC,IAAI,CAACuC,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcL,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CACnD,CAAC;IACH;;IAEA;IACA,IAAI5H,OAAO,CAACE,KAAK,EAAE;MACjByH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACE,mBAAmB,KAAKlG,OAAO,CAACE,KAAK,CAAC;IAChF;IACA,IAAIF,OAAO,CAACG,SAAS,EAAE;MACrBwH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAAC7F,SAAS,KAAKH,OAAO,CAACG,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIH,OAAO,CAACU,cAAc,EAAE;MAC1B,IAAIV,OAAO,CAACU,cAAc,KAAK,aAAa,EAAE;QAC5CiH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAC7B9G,cAAc,CAACsJ,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAC1E,OAAO,KAAKyD,IAAI,CAACzD,OAAO,CAC3D,CAAC;MACH,CAAC,MAAM,IAAIvC,OAAO,CAACU,cAAc,KAAK,iBAAiB,EAAE;QACvDiH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAC7B,CAAC9G,cAAc,CAACsJ,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAC1E,OAAO,KAAKyD,IAAI,CAACzD,OAAO,CAC5D,CAAC;MACH;IACF;;IAEA;IACAoF,QAAQ,CAAC5C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIwD,MAAM,GAAGzD,CAAC,CAAClE,MAAM,CAAC;MACtB,IAAI4H,MAAM,GAAGzD,CAAC,CAACnE,MAAM,CAAC;;MAEtB;MACA,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxB,MAAMoE,YAAY,GAAIC,EAAE,IAAK;UAC3B,MAAMC,KAAK,GAAGD,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;UAC/B,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC3C,CAAC;QACD,MAAMuD,IAAI,GAAGzD,YAAY,CAACuD,MAAM,CAAC;QACjC,MAAMG,IAAI,GAAG1D,YAAY,CAACwD,MAAM,CAAC;QAEjC,IAAI1H,SAAS,KAAK,KAAK,EAAE;UACvB,OAAO2H,IAAI,GAAGC,IAAI;QACpB,CAAC,MAAM;UACL,OAAOA,IAAI,GAAGD,IAAI;QACpB;MACF;;MAEA;MACA,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACZ,WAAW,CAAC,CAAC;QAC7Ba,MAAM,GAAGA,MAAM,CAACb,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI7G,SAAS,KAAK,KAAK,EAAE;QACvB,OAAOyH,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF/I,eAAe,CAACgI,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAM3D,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI2D,QAAQ,GAAGzI,cAAc;;IAE7B;IACA,IAAIM,UAAU,EAAE;MACd,MAAMoI,WAAW,GAAGpI,UAAU,CAACqI,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI;QAAA,IAAA4B,eAAA,EAAAC,qBAAA,EAAAC,UAAA;QAAA,OAC7B9B,IAAI,CAAC1E,OAAO,CAACsF,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAiB,eAAA,GAChD5B,IAAI,CAAC7G,SAAS,cAAAyI,eAAA,uBAAdA,eAAA,CAAgBhB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAkB,qBAAA,GACnD7B,IAAI,CAAC+B,kBAAkB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBjB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAmB,UAAA,GAC5D9B,IAAI,CAACnE,IAAI,cAAAiG,UAAA,uBAATA,UAAA,CAAWlB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CAChD,CAAC;IACH;;IAEA;IACA,IAAI5H,OAAO,CAACI,SAAS,EAAE;MACrBuH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC7G,SAAS,KAAKJ,OAAO,CAACI,SAAS,CAAC;IAC1E;IACA,IAAIJ,OAAO,CAACS,SAAS,EAAE;MACrBkH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACxG,SAAS,KAAKT,OAAO,CAACS,SAAS,CAAC;IAC1E;IACA,IAAIT,OAAO,CAACQ,aAAa,EAAE;MACzBmH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChE,gBAAgB,KAAKjD,OAAO,CAACQ,aAAa,CAAC;IACrF;IACA,IAAIR,OAAO,CAACK,UAAU,EAAE;MACtBsH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC9G,OAAO,CAACK,UAAU,CACnE,CAAC;IACH;IACA,IAAIL,OAAO,CAACM,QAAQ,EAAE;MACpBqH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAAC9G,OAAO,CAACM,QAAQ,CACjE,CAAC;IACH;IACA,IAAIN,OAAO,CAACO,gBAAgB,EAAE;MAC5B,MAAM0I,MAAM,GAAGC,UAAU,CAAClJ,OAAO,CAACO,gBAAgB,CAAC;MACnDoH,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAC7BiC,UAAU,CAACjC,IAAI,CAACrE,iBAAiB,CAAC,IAAIqG,MACxC,CAAC;IACH;;IAEA;IACAtB,QAAQ,CAAC5C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIwD,MAAM,GAAGzD,CAAC,CAAClE,MAAM,CAAC;MACtB,IAAI4H,MAAM,GAAGzD,CAAC,CAACnE,MAAM,CAAC;MAEtB,IAAIA,MAAM,KAAK,qBAAqB,EAAE;QACpC2H,MAAM,GAAG,IAAI3B,IAAI,CAAC2B,MAAM,CAAC;QACzBC,MAAM,GAAG,IAAI5B,IAAI,CAAC4B,MAAM,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrCA,MAAM,GAAGA,MAAM,CAACZ,WAAW,CAAC,CAAC;QAC7Ba,MAAM,GAAGA,MAAM,CAACb,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI7G,SAAS,KAAK,KAAK,EAAE;QACvB,OAAOyH,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF7I,yBAAyB,CAAC8H,QAAQ,CAAC;EACrC,CAAC;;EAED;EACA,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAInK,SAAS,KAAK,CAAC,EAAE;MACnByF,YAAY,CAAC,gEAAgE,EAAE,SAAS,CAAC;MACzF;IACF;IACA9C,WAAW,CAAC,CAACD,QAAQ,CAAC;IACtBD,gBAAgB,CAAC,EAAE,CAAC;IACpBgD,YAAY,CACV,CAAC/C,QAAQ,GACL,4EAA4E,GAC5E,gCAAgC,EACpC,MACF,CAAC;EACH,CAAC;EAED,MAAM0H,mBAAmB,GAAIC,MAAM,IAAK;IACtC5H,gBAAgB,CAAC6H,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACnB,QAAQ,CAACkB,MAAM,CAAC,GACtCC,IAAI,CAACtC,MAAM,CAAC7B,EAAE,IAAIA,EAAE,KAAKkE,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MAErB5E,YAAY,CACV,GAAG8E,YAAY,CAAC7C,MAAM,6BAA6B,EACnD,MACF,CAAC;MACD,OAAO6C,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxK,SAAS,KAAK,CAAC,EAAE;IAErB,MAAMyK,MAAM,GAAG7J,sBAAsB,CAAC8J,GAAG,CAACzC,IAAI,IAAIA,IAAI,CAAC0C,iBAAiB,CAAC;IACzElI,gBAAgB,CAACgI,MAAM,CAAC;IACxBhF,YAAY,CAAC,YAAYgF,MAAM,CAAC/C,MAAM,6BAA6B,EAAE,SAAS,CAAC;EACjF,CAAC;EAED,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAC3BnI,gBAAgB,CAAC,EAAE,CAAC;IACpBgD,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMoF,iBAAiB,GAAIC,MAAM,IAAK;IACpC,OAAO5K,cAAc,CAACsJ,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAC1E,OAAO,KAAKuH,MAAM,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMC,gCAAgC,GAAI/D,IAAI,IAAK;IACjD,MAAMC,YAAY,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAC1CF,IAAI,CAACE,mBAAmB,KAAK,YAAY,IACzCF,IAAI,CAACE,mBAAmB,KAAK,QAAQ;IAEzD,IAAI,CAACD,YAAY,EAAE;MACjB,OAAO,yEAAyE;IAClF;IAEA,OAAO,+CAA+C;EACxD,CAAC;;EAED;EACA,MAAM+D,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CjL,YAAY,CAACiL,QAAQ,CAAC;IACtBtJ,cAAc,CAAC,CAAC,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBQ,UAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAM+J,gBAAgB,GAAG,MAAAA,CAAOC,kBAAkB,GAAG,IAAI,KAAK;IAC5D/I,aAAa,CAAC,QAAQ,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,MAAMgE,eAAe,CAAC,CAAC;;IAEvB;IACA,IAAI6E,kBAAkB,EAAE;MACtB9H,WAAW,CAAC;QACVC,OAAO,EAAE6H,kBAAkB,CAAC7H,OAAO;QACnCC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE0H,kBAAkB,CAACC,eAAe,IAAID,kBAAkB,CAACE,aAAa,IAAI,EAAE;QAChG3H,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;MACFwB,YAAY,CAAC,QAAQ2F,kBAAkB,CAAC7H,OAAO,8BAA8B,EAAE,SAAS,CAAC;IAC3F,CAAC,MAAM;MACL;MACAD,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IAEA9B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoJ,WAAW,GAAGA,CAAA,KAAM;IACxBpJ,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAMmJ,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzCpI,WAAW,CAACgH,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACmB,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAI3E,IAAI,IAAK;IACjC1D,WAAW,CAACgH,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP/G,OAAO,EAAEyD,IAAI,CAACzD,OAAO;MACrBG,kBAAkB,EAAEsD,IAAI,CAACqE,eAAe,IAAIrE,IAAI,CAACsE,aAAa,IAAI;IACpE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,qBAAqB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,YAAY,GAAG,UAAU,KAAK;IACzE,IAAI;MACF;MACA,MAAM7M,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAE8M,OAAO;MAExE,IAAIC,iBAAiB,GAAG,KAAK;MAC7B,IAAIC,eAAe,GAAG,KAAK;;MAE3B;MACA,IAAI;QACF,MAAMhN,WAAW,CAACiN,WAAW,CAACxM,UAAU,EAAEmM,MAAM,EAAE,UAAU,EAAEC,YAAY,CAAC;QAC3EE,iBAAiB,GAAG,IAAI;QACxBpG,OAAO,CAAC6B,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC,CAAC,OAAOjC,KAAK,EAAE;QACd,IAAIA,KAAK,CAAC2G,MAAM,IAAI3G,KAAK,CAAC2G,MAAM,CAAChD,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC1D6C,iBAAiB,GAAG,IAAI;UACxBpG,OAAO,CAAC6B,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,MAAM;UACL7B,OAAO,CAACJ,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;UAC9D,MAAMA,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMvG,WAAW,CAACiN,WAAW,CAACxM,UAAU,EAAEmM,MAAM,EAAE,QAAQ,EAAEC,YAAY,CAAC;QACzEG,eAAe,GAAG,IAAI;QACtBrG,OAAO,CAAC6B,GAAG,CAAC,oCAAoC,CAAC;MACnD,CAAC,CAAC,OAAOjC,KAAK,EAAE;QACd,IAAIA,KAAK,CAAC2G,MAAM,IAAI3G,KAAK,CAAC2G,MAAM,CAAChD,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC1D8C,eAAe,GAAG,IAAI;UACtBrG,OAAO,CAAC6B,GAAG,CAAC,2BAA2B,CAAC;QAC1C,CAAC,MAAM;UACL7B,OAAO,CAACJ,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D,MAAMA,KAAK;QACb;MACF;MAEA,OAAOwG,iBAAiB,IAAIC,eAAe;IAC7C,CAAC,CAAC,OAAOzG,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM4G,kCAAkC,GAAG,MAAOpF,IAAI,IAAK;IACzD,IAAI;MACF,MAAMK,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;MAC3C,MAAMgF,iBAAiB,GAAGhF,YAAY,KAAK,CAAC,GAAG,eAAe,GACrCA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9CA,YAAY,KAAK,CAAC,GAAG,uBAAuB,GAC5C,mBAAmB;MAE5C,MAAMiF,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,oCAAoCxF,IAAI,CAACzD,OAAO,MAAM,GACtD,kBAAkB8I,iBAAiB,MAAM,GACzC,kFAAkF,GAClF,iBACF,CAAC;MAED,IAAI,CAACC,QAAQ,EAAE;QACb;MACF;MAEAlJ,sBAAsB,CAAC,IAAI,CAAC;MAC5BqC,YAAY,CAAC,8CAA8C,EAAE,MAAM,CAAC;MAEpE,MAAMmG,qBAAqB,CAAC5E,IAAI,CAACzD,OAAO,EAAE,UAAU,CAAC;MACrDkC,YAAY,CAAC,QAAQuB,IAAI,CAACzD,OAAO,+CAA+C,EAAE,SAAS,CAAC;;MAE5F;MACA,MAAM4B,QAAQ,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,YAAY,CAAC,sCAAsC,IAAID,KAAK,CAACzC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACzG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMqJ,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACF,IAAI,CAACpJ,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAiB,EAAE;QACxG6B,YAAY,CAAC,mCAAmC,EAAE,SAAS,CAAC;QAC5D;MACF;;MAEA;MACA,MAAMuB,IAAI,GAAG5G,IAAI,CAACsM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpJ,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;MAC3D,IAAI,CAACyD,IAAI,EAAE;QACTvB,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC;QACzC;MACF;MAEA,IAAI,CAACsB,oBAAoB,CAACC,IAAI,CAAC,EAAE;QAC/B,MAAM4F,SAAS,GAAG7B,gCAAgC,CAAC/D,IAAI,CAAC;QACxDvB,YAAY,CAAC,oCAAoCmH,SAAS,EAAE,EAAE,OAAO,CAAC;QACtE;MACF;;MAEA;MACA,IAAI/B,iBAAiB,CAACxH,QAAQ,CAACE,OAAO,CAAC,EAAE;QACvCkC,YAAY,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI,CAAC0B,eAAe,CAACH,IAAI,CAAC,EAAE;QAC1B,MAAMqF,iBAAiB,GAAGrF,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1CL,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,yBAAyB,GACnDL,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,uBAAuB,GACjD,mBAAmB;QAE5C,MAAMiF,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,uBAAuBxF,IAAI,CAACzD,OAAO,2CAA2C,GAC9E,uBAAuB8I,iBAAiB,MAAM,GAC9C,gGAAgG,GAChG,iFACF,CAAC;QAED,IAAI,CAACC,QAAQ,EAAE;UACb;QACF;;QAEA;QACA,IAAI;UACFlJ,sBAAsB,CAAC,IAAI,CAAC;UAC5BqC,YAAY,CAAC,8CAA8C,EAAE,MAAM,CAAC;UAEpE,MAAMmG,qBAAqB,CAAC5E,IAAI,CAACzD,OAAO,EAAE,UAAU,CAAC;UACrDkC,YAAY,CAAC,mDAAmD,EAAE,SAAS,CAAC;;UAE5E;UACA,MAAMN,QAAQ,CAAC,CAAC;QAClB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdC,YAAY,CAAC,sCAAsC,IAAID,KAAK,CAACzC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;UACvG;QACF;MACF;MAEAK,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMpE,qBAAqB,CAAC6N,oBAAoB,CAACnN,UAAU,EAAE2D,QAAQ,CAAC;MACtEoC,YAAY,CAAC,oCAAoC,EAAE,SAAS,CAAC;MAC7D8F,WAAW,CAAC,CAAC;MACb,MAAMlG,kBAAkB,CAAC,CAAC;MAC1BJ,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,YAAY,CAAC,+CAA+C,IAAID,KAAK,CAACzC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAClH,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAM0J,iBAAiB,GAAG,MAAOpL,cAAc,IAAK;IAClD,IAAI;MACF0B,sBAAsB,CAAC,IAAI,CAAC;MAC5BqC,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;MAEnDG,OAAO,CAAC6B,GAAG,CAAC,kDAAkD,EAAE;QAC9DkD,iBAAiB,EAAEjJ,cAAc,CAACiJ,iBAAiB;QACnDX,kBAAkB,EAAEtI,cAAc,CAACsI,kBAAkB;QACrDzG,OAAO,EAAE7B,cAAc,CAAC6B,OAAO;QAC/BwJ,WAAW,EAAErN;MACf,CAAC,CAAC;MAEF,MAAMsN,QAAQ,GAAG,MAAMhO,qBAAqB,CAACiO,WAAW,CAACvN,UAAU,EAAEgC,cAAc,CAACiJ,iBAAiB,CAAC;MAEtG,IAAIqC,QAAQ,CAACE,QAAQ,EAAE;QACrB;QACA,MAAMC,SAAS,GAAGZ,MAAM,CAACzJ,IAAI,CAACkK,QAAQ,CAACE,QAAQ,EAAE,QAAQ,CAAC;QAC1D,IAAIC,SAAS,EAAE;UACb1H,YAAY,CAAC,6CAA6C,EAAE,SAAS,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAM2H,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,QAAQ,CAACE,QAAQ;UAC7BE,IAAI,CAACI,QAAQ,GAAG,kBAAkB9L,cAAc,CAACsI,kBAAkB,MAAM;UACzEqD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/B3H,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAClE;MACF,CAAC,MAAM,IAAIuH,QAAQ,CAACa,WAAW,EAAE;QAC/B;QACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAAChB,QAAQ,CAACa,WAAW,CAAC,CAAC,EAAE;UAAEI,IAAI,EAAE;QAAkB,CAAC,CAAC;QAChF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QACrC,MAAMV,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGW,GAAG;QACfd,IAAI,CAACI,QAAQ,GAAG,kBAAkB9L,cAAc,CAACsI,kBAAkB,MAAM;QACzEqD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/Be,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxBzI,YAAY,CAAC,4BAA4B,EAAE,SAAS,CAAC;MACvD,CAAC,MAAM;QACLA,YAAY,CAAC,sCAAsC,EAAE,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,YAAY,CAAC,oCAAoC,IAAID,KAAK,CAACzC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACvG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMkL,0BAA0B,GAAG,MAAO5M,cAAc,IAAK;IAC3D,IAAI6K,MAAM,CAACC,OAAO,CAAC,mDAAmD9K,cAAc,CAACsI,kBAAkB,GAAG,CAAC,EAAE;MAC3G,IAAI;QACF5G,sBAAsB,CAAC,IAAI,CAAC;QAC5B,MAAMpE,qBAAqB,CAACuP,oBAAoB,CAAC7O,UAAU,EAAEgC,cAAc,CAACiJ,iBAAiB,CAAC;QAC9FlF,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAChE,MAAMJ,kBAAkB,CAAC,CAAC;QAC1BJ,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,YAAY,CAAC,kDAAkD,IAAID,KAAK,CAACzC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;MACrH,CAAC,SAAS;QACRK,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAMoL,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIhM,aAAa,CAACkF,MAAM,KAAK,CAAC,EAAE;MAC9BjC,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI8G,MAAM,CAACC,OAAO,CAAC,iCAAiChK,aAAa,CAACkF,MAAM,kBAAkB,CAAC,EAAE;MAC3F,IAAI;QACFtE,sBAAsB,CAAC,IAAI,CAAC;QAC5B,KAAK,MAAM+C,EAAE,IAAI3D,aAAa,EAAE;UAC9B,MAAMxD,qBAAqB,CAACuP,oBAAoB,CAAC7O,UAAU,EAAEyG,EAAE,CAAC;QAClE;QACAV,YAAY,CAAC,GAAGjD,aAAa,CAACkF,MAAM,wCAAwC,EAAE,SAAS,CAAC;QACxFjF,gBAAgB,CAAC,EAAE,CAAC;QACpB,MAAM4C,kBAAkB,CAAC,CAAC;QAC1BJ,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;MACzE,CAAC,SAAS;QACRrC,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAMqL,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIjM,aAAa,CAACkF,MAAM,KAAK,CAAC,EAAE;MAC9BjC,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI;MACFrC,sBAAsB,CAAC,IAAI,CAAC;MAC5B;MACA,MAAMsL,aAAa,GAAGxO,cAAc,CAAC8H,MAAM,CAACC,IAAI,IAC9CzF,aAAa,CAAC2G,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAC/C,CAAC;;MAED;MACA,MAAMgE,UAAU,GAAGC,WAAW,CAACF,aAAa,CAAC;MAC7CG,WAAW,CAACF,UAAU,EAAE,kBAAkB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;MAEvFtJ,YAAY,CAAC,GAAGjD,aAAa,CAACkF,MAAM,2BAA2B,EAAE,SAAS,CAAC;IAC7E,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRrC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMwL,WAAW,GAAIlJ,IAAI,IAAK;IAC5B,MAAMsJ,OAAO,GAAG,CAAC,SAAS,EAAE,oBAAoB,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAC3H,MAAMC,IAAI,GAAGvJ,IAAI,CAACgF,GAAG,CAACzC,IAAI,IAAI,CAC5BA,IAAI,CAAC1E,OAAO,EACZ0E,IAAI,CAAC+B,kBAAkB,EACvB,IAAIlC,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC,CAAC,EACvDjH,IAAI,CAAC7G,SAAS,EACd6G,IAAI,CAACxG,SAAS,EACdwG,IAAI,CAACvE,kBAAkB,EACvBuE,IAAI,CAACrE,iBAAiB,EACtBqE,IAAI,CAAChE,gBAAgB,CACtB,CAAC;IAEF,OAAO,CAAC+K,OAAO,EAAE,GAAGC,IAAI,CAAC,CAACvE,GAAG,CAACyE,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC;EAED,MAAMP,WAAW,GAAGA,CAACQ,OAAO,EAAEC,QAAQ,KAAK;IACzC,MAAMxB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACsB,OAAO,CAAC,EAAE;MAAEpB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACrE,MAAMb,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,IAAIF,IAAI,CAACI,QAAQ,KAAK+B,SAAS,EAAE;MAC/B,MAAMrB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;MACrCV,IAAI,CAACoC,YAAY,CAAC,MAAM,EAAEtB,GAAG,CAAC;MAC9Bd,IAAI,CAACoC,YAAY,CAAC,UAAU,EAAEF,QAAQ,CAAC;MACvClC,IAAI,CAACqC,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCrC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMhB,UAAU,GAAGC,WAAW,CAAChO,sBAAsB,CAAC;IACtDiO,WAAW,CAACF,UAAU,EAAE,wBAAwB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7FtJ,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC;EACpD,CAAC;;EAED;EACAhL,mBAAmB,CAACoF,GAAG,EAAE,OAAO;IAC9B+P,kBAAkB,EAAGC,MAAM,IAAK;MAC9B,IAAIA,MAAM,KAAK,oBAAoB,EAAE;QACnC1E,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAI0E,MAAM,KAAK,0BAA0B,EAAE;QAChD5P,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;IACD6P,0BAA0B,EAAG9I,IAAI,IAAK;MACpCmE,gBAAgB,CAACnE,IAAI,CAAC;IACxB,CAAC;IACD+I,wBAAwB,EAAG/I,IAAI,IAAK;MAClC;MACA,MAAMtF,cAAc,GAAGxB,cAAc,CAACwM,IAAI,CAACzE,IAAI,IAAIA,IAAI,CAAC1E,OAAO,KAAKyD,IAAI,CAACzD,OAAO,CAAC;MACjF,IAAI7B,cAAc,EAAE;QAClBa,eAAe,CAACb,cAAc,CAAC;QAC/BW,aAAa,CAAC,MAAM,CAAC;QACrBF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLsD,YAAY,CAAC,4CAA4C,EAAE,OAAO,CAAC;MACrE;IACF,CAAC;IACDuK,kBAAkB,EAAGhJ,IAAI,IAAK;MAC5B;MACApB,OAAO,CAAC6B,GAAG,CAAC,mDAAmDT,IAAI,CAACzD,OAAO,EAAE,CAAC;MAC9EqC,OAAO,CAAC6B,GAAG,CAAC,4CAA4C,EAAEvH,cAAc,CAACwK,GAAG,CAACiC,CAAC,KAAK;QAAExG,EAAE,EAAEwG,CAAC,CAAChC,iBAAiB;QAAE3D,IAAI,EAAE2F,CAAC,CAACpJ;MAAQ,CAAC,CAAC,CAAC,CAAC;MAElI,MAAM7B,cAAc,GAAGxB,cAAc,CAACwM,IAAI,CAACzE,IAAI,IAAIA,IAAI,CAAC1E,OAAO,KAAKyD,IAAI,CAACzD,OAAO,CAAC;MACjF,IAAI7B,cAAc,EAAE;QAClBkE,OAAO,CAAC6B,GAAG,CAAC,wCAAwC,EAAE/F,cAAc,CAAC;QACrEoL,iBAAiB,CAACpL,cAAc,CAAC;MACnC,CAAC,MAAM;QACLkE,OAAO,CAAC6B,GAAG,CAAC,yDAAyDT,IAAI,CAACzD,OAAO,EAAE,CAAC;QACpFkC,YAAY,CAAC,4CAA4C,EAAE,OAAO,CAAC;MACrE;IACF;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMwK,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,CAACxO,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAMuO,QAAQ,GAAGD,UAAU,GAAGtO,YAAY;IAC1C,OAAOqO,KAAK,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAME,aAAa,GAAIJ,KAAK,IAAKvI,IAAI,CAAC4I,IAAI,CAACL,KAAK,CAACxI,MAAM,GAAG7F,YAAY,CAAC;;EAIvE;EACA,MAAM2O,eAAe,GAAGA,CAAA,kBACtBpR,OAAA,CAACvE,KAAK;IAAC4V,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7CzR,OAAA,CAAC3C,KAAK;MAACqU,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnGzR,OAAA,CAAC3C,KAAK;QAACqU,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDzR,OAAA,CAACX,SAAS;UAAC0S,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CpS,OAAA,CAAC1E,GAAG;UAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9DvM,UAAU,CAACE;UAAU;YAAA6M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbpS,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERpS,OAAA,CAAC3C,KAAK;QAACqU,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDzR,OAAA,CAAC7B,SAAS;UAAC4T,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CpS,OAAA,CAAC1E,GAAG;UAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9DvM,UAAU,CAACG;UAAe;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbpS,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERpS,OAAA,CAAC3C,KAAK;QAACqU,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDzR,OAAA,CAACT,SAAS;UAACwS,KAAK,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CpS,OAAA,CAAC1E,GAAG;UAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9DvM,UAAU,CAACgE;UAAiB;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACbpS,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERpS,OAAA,CAAC3C,KAAK;QAACqU,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDzR,OAAA,CAAC1E,GAAG;UAAC+V,EAAE,EAAE;YACPmB,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBlB,OAAO,EAAEtM,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAC1DL,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAClFoN,OAAO,EAAE,MAAM;YACff,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACAzR,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAACP,KAAK,EAAC,OAAO;YAAAN,QAAA,GAC1DvM,UAAU,CAACK,wBAAwB,EAAC,GACvC;UAAA;YAAA0M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNpS,OAAA,CAAC1E,GAAG;UAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAAC;UAEvE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpS,OAAA,CAACzE,UAAU;YAAC8W,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,GACjDvM,UAAU,CAACM,kBAAkB,EAAC,OACjC;UAAA;YAAAyM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;EACA,MAAMQ,sBAAsB,GAAGA,CAAA,kBAC7B5S,OAAA,CAACvE,KAAK;IAAC4V,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAE,QAAA,gBACzBzR,OAAA,CAACtE,IAAI;MAACmX,SAAS;MAAClB,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAAAH,QAAA,gBAC7CzR,OAAA,CAACtE,IAAI;QAACoX,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;UACRgX,SAAS;UACTC,WAAW,EAAC,0CAA0C;UACtD5G,KAAK,EAAElL,UAAW;UAClB+R,QAAQ,EAAGC,CAAC,IAAK/R,aAAa,CAAC+R,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;UAC/CgH,UAAU,EAAE;YACVC,cAAc,eACZvT,OAAA,CAAC7C,cAAc;cAACqW,QAAQ,EAAC,OAAO;cAAA/B,QAAA,eAC9BzR,OAAA,CAACjC,UAAU;gBAAAkU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACjB;YACDqB,YAAY,EAAErS,UAAU,iBACtBpB,OAAA,CAAC7C,cAAc;cAACqW,QAAQ,EAAC,KAAK;cAAA/B,QAAA,eAC5BzR,OAAA,CAACjD,UAAU;gBAAC2W,OAAO,EAAEA,CAAA,KAAMrS,aAAa,CAAC,EAAE,CAAE;gBAACsS,IAAI,EAAC,OAAO;gBAAAlC,QAAA,eACxDzR,OAAA,CAAC/B,SAAS;kBAAAgU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;QAACoX,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvBzR,OAAA,CAACxE,MAAM;UACLyX,SAAS;UACTZ,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAEA,CAAA,KAAM/R,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5DqQ,KAAK,EAAE6B,MAAM,CAACC,MAAM,CAACjS,OAAO,CAAC,CAACwI,IAAI,CAAC0J,CAAC,IAAIA,CAAC,CAAC,GAAG,SAAS,GAAG,SAAU;UAAArC,QAAA,GACpE,SACQ,EAACmC,MAAM,CAACC,MAAM,CAACjS,OAAO,CAAC,CAACgH,MAAM,CAACkL,CAAC,IAAIA,CAAC,CAAC,CAACxL,MAAM,GAAG,CAAC,IAAI,IAAIsL,MAAM,CAACC,MAAM,CAACjS,OAAO,CAAC,CAACgH,MAAM,CAACkL,CAAC,IAAIA,CAAC,CAAC,CAACxL,MAAM,GAAG;QAAA;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPpS,OAAA,CAACtE,IAAI;QAACoX,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvBzR,OAAA,CAACxE,MAAM;UACLyX,SAAS;UACTZ,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAE3I,cAAe;UACxBgH,KAAK,EAAEzO,QAAQ,GAAG,WAAW,GAAG,SAAU;UAC1CyQ,QAAQ,EAAEnT,SAAS,KAAK,CAAE;UAAA6Q,QAAA,EAEzBnO,QAAQ,GAAG,gBAAgB,GAAG;QAAoB;UAAA2O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPpS,OAAA,CAACtE,IAAI;QAACoX,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvBzR,OAAA,CAACxE,MAAM;UACLyX,SAAS;UACTZ,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAEnD,eAAgB;UACzBwD,QAAQ,EAAEnT,SAAS,KAAK,CAAC,IAAIY,sBAAsB,CAAC8G,MAAM,KAAK,CAAE;UAAAmJ,QAAA,EAEhE7Q,SAAS,KAAK,CAAC,GAAG,8BAA8B,GAAG;QAAwB;UAAAqR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPpS,OAAA,CAACtE,IAAI;QAACoX,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvBzR,OAAA,CAACxE,MAAM;UACLyX,SAAS;UACTZ,OAAO,EAAC,WAAW;UACnBqB,OAAO,EAAE3H,gBAAiB;UAAA0F,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPpS,OAAA,CAACvC,QAAQ;MAACuW,EAAE,EAAEtS,mBAAoB;MAAA+P,QAAA,gBAChCzR,OAAA,CAAC5C,OAAO;QAACiU,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE;MAAE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BpS,OAAA,CAACzE,UAAU;QAAC8W,OAAO,EAAC,OAAO;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,EAC9D7Q,SAAS,KAAK,CAAC,GAAG,iBAAiB,GAAG;MAA2B;QAAAqR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAEbpS,OAAA,CAACtE,IAAI;QAACmX,SAAS;QAAClB,OAAO,EAAE,CAAE;QAAAF,QAAA,GAExB7Q,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAAuR,QAAA,gBACEzR,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjCzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAmB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5CpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAE1K,OAAO,CAACE,KAAM;gBACrBqR,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,KAAK,EAAEsR,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAEjEzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,YAAY;kBAAAmF,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClDpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,gBAAgB;kBAAAmF,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1DpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,UAAU;kBAAAmF,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjCzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAE1K,OAAO,CAACG,SAAU;gBACzBoR,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEG,SAAS,EAAEqR,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAErEzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAI8B,GAAG,CAAClT,IAAI,CAACsK,GAAG,CAACiC,CAAC,IAAIA,CAAC,CAACxL,SAAS,CAAC,CAAC,CAAC,CAAC6G,MAAM,CAACuL,OAAO,CAAC,CAAC7I,GAAG,CAAC8I,GAAG,iBAC/DpU,OAAA,CAAC3D,QAAQ;kBAAWiQ,KAAK,EAAE8H,GAAI;kBAAA3C,QAAA,EAAE2C;gBAAG,GAArBA,GAAG;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjCzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7CpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAE1K,OAAO,CAACU,cAAe;gBAC9B6Q,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEU,cAAc,EAAE8Q,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAE1EzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,aAAa;kBAAAmF,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpDpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,iBAAiB;kBAAAmF,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,eACP,CACH,EAGAxR,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAAuR,QAAA,gBACEzR,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjCzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClCpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAE1K,OAAO,CAACI,SAAU;gBACzBmR,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,SAAS,EAAEoR,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAErEzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAI8B,GAAG,CAACpT,cAAc,CAACwK,GAAG,CAACiC,CAAC,IAAIA,CAAC,CAACvL,SAAS,CAAC,CAAC,CAAC,CAAC4G,MAAM,CAACuL,OAAO,CAAC,CAAC7I,GAAG,CAAC+I,EAAE,iBACxErU,OAAA,CAAC3D,QAAQ;kBAAUiQ,KAAK,EAAE+H,EAAG;kBAAA5C,QAAA,EAAE4C;gBAAE,GAAlBA,EAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2B,CAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjCzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvCpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAE1K,OAAO,CAACQ,aAAc;gBAC7B+Q,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEQ,aAAa,EAAEgR,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAEzEzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnCpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,UAAU;kBAAAmF,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9CpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,cAAc;kBAAAmF,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtDpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,eAAe;kBAAAmF,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZW,KAAK,EAAC,0BAAqB;cAC3BzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAE1K,OAAO,CAACO,gBAAiB;cAChCgR,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEO,gBAAgB,EAAEiR,CAAC,CAACC,MAAM,CAAC/G;cAAK,CAAC,CAAE;cAC5E4G,WAAW,EAAC;YAAS;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZW,KAAK,EAAC,aAAa;cACnBzF,IAAI,EAAC,MAAM;cACXvC,KAAK,EAAE1K,OAAO,CAACK,UAAW;cAC1BkR,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,UAAU,EAAEmR,CAAC,CAACC,MAAM,CAAC/G;cAAK,CAAC,CAAE;cACtEiI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZW,KAAK,EAAC,WAAW;cACjBzF,IAAI,EAAC,MAAM;cACXvC,KAAK,EAAE1K,OAAO,CAACM,QAAS;cACxBiR,QAAQ,EAAGC,CAAC,IAAKvR,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEM,QAAQ,EAAEkR,CAAC,CAACC,MAAM,CAAC/G;cAAK,CAAC,CAAE;cACpEiI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH,eAEDpS,OAAA,CAACtE,IAAI;UAACoX,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAtB,QAAA,eAChBzR,OAAA,CAAC3C,KAAK;YAACqU,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACE,cAAc,EAAC,UAAU;YAAAJ,QAAA,eAC1DzR,OAAA,CAACxE,MAAM;cACL6W,OAAO,EAAC,UAAU;cAClBsB,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEA,CAAA,KAAM7R,UAAU,CAAC;gBACxBC,KAAK,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,UAAU,EAAE,EAAE;gBACvDC,QAAQ,EAAE,EAAE;gBAAEC,gBAAgB,EAAE,EAAE;gBAAEC,aAAa,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBACpEC,cAAc,EAAE;cAClB,CAAC,CAAE;cAAAmP,QAAA,EACJ;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGV9O,QAAQ,IAAIF,aAAa,CAACkF,MAAM,GAAG,CAAC,iBACnCtI,OAAA,CAAAE,SAAA;MAAAuR,QAAA,gBACEzR,OAAA,CAAC5C,OAAO;QAACiU,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE;MAAE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BpS,OAAA,CAAC3C,KAAK;QAACqU,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBACpDzR,OAAA,CAACzE,UAAU;UAAC8W,OAAO,EAAC,OAAO;UAAAZ,QAAA,GACxBrO,aAAa,CAACkF,MAAM,EAAC,uBACxB;QAAA;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpS,OAAA,CAACxE,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAEtI,cAAe;UAAAqG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpS,OAAA,CAACxE,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAElI,cAAe;UAAAiG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpS,OAAA,CAACxE,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAErE,gBAAiB;UAAAoC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpS,OAAA,CAACxE,MAAM;UACLmY,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBN,KAAK,EAAC,OAAO;UACb2B,OAAO,EAAEtE,gBAAiB;UAAAqC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACR,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAMqC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAG7D,mBAAmB,CAACvP,YAAY,CAAC;IAEtD,IAAIA,YAAY,CAACgH,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACEtI,OAAA,CAACxD,KAAK;QAACoH,QAAQ,EAAC,MAAM;QAAA6N,QAAA,EACnBrQ,UAAU,IAAIQ,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,GAC7C,4CAA4C,GAC5C;MAAyB;QAAAkQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAEZ;IAEA,oBACEpS,OAAA,CAAAE,SAAA;MAAAuR,QAAA,gBACEzR,OAAA,CAACpD,cAAc;QAAC+X,SAAS,EAAElZ,KAAM;QAAAgW,QAAA,eAC/BzR,OAAA,CAACvD,KAAK;UAACkX,IAAI,EAAC,OAAO;UAAAlC,QAAA,gBACjBzR,OAAA,CAACnD,SAAS;YAAA4U,QAAA,eACRzR,OAAA,CAAClD,QAAQ;cAAA2U,QAAA,gBACPzR,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAAC3C,KAAK;kBAACqU,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpDzR,OAAA,CAACzE,UAAU;oBAAC8W,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClEpS,OAAA,CAACjD,UAAU;oBAAC4W,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtC/Q,SAAS,CAAC,SAAS,CAAC;sBACpBE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA6O,QAAA,EACC/O,MAAM,KAAK,SAAS,GAAIE,SAAS,KAAK,KAAK,gBAAG5C,OAAA,CAACvB,cAAc;sBAAAwT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAACzB,cAAc;sBAAA0T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIpS,OAAA,CAACzB,cAAc;sBAAA0T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZpS,OAAA,CAACtD,SAAS;YAAA+U,QAAA,EACPiD,YAAY,CAACpJ,GAAG,CAAE1D,IAAI,IAAK;cAC1B,MAAMgN,aAAa,GAAGnJ,iBAAiB,CAAC7D,IAAI,CAACzD,OAAO,CAAC;cACrD,MAAM0Q,cAAc,GAAGlN,oBAAoB,CAACC,IAAI,CAAC;cACjD,MAAMkN,eAAe,GAAG,CAACD,cAAc,GAAGlJ,gCAAgC,CAAC/D,IAAI,CAAC,GAAG,EAAE;cAErF,oBACE5H,OAAA,CAAClD,QAAQ;gBAAA2U,QAAA,gBACPzR,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,eACRzR,OAAA,CAACzE,UAAU;oBAAC8W,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,QAAQ;oBAAAb,QAAA,EAC5C7J,IAAI,CAACzD;kBAAO;oBAAA8N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,EAAE7J,IAAI,CAAC7F;gBAAS;kBAAAkQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvCpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,EAAE7J,IAAI,CAACsC;gBAAO;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,EAAE7J,IAAI,CAACoC;gBAAmB;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,EAAE7J,IAAI,CAACqC;gBAAiB;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,GAAE7J,IAAI,CAACqE,eAAe,IAAIrE,IAAI,CAACsE,aAAa,EAAC,IAAE;gBAAA;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrEpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,eACRzR,OAAA,CAAC1C,IAAI;oBACHqW,IAAI,EAAC,OAAO;oBACZW,KAAK,EAAE1M,IAAI,CAACE,mBAAoB;oBAChCiK,KAAK,EAAEnK,IAAI,CAACE,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;kBAAU;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,EACP,CAAC,MAAM;oBACN,MAAMxJ,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;oBAC3C,MAAM8M,iBAAiB,GAAG9M,YAAY,KAAK,CAAC,GAAG,eAAe,GACrCA,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,aAAa,GAClCA,YAAY,KAAK,CAAC,GAAG,UAAU,GAC/B,aAAa;oBACtC,MAAM+M,MAAM,GAAG/M,YAAY,KAAK,CAAC,GAAG,SAAS,GAC/BA,YAAY,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;oBAEtD,oBACEjI,OAAA,CAACzC,OAAO;sBAAC0X,KAAK,EAAE,aAAarN,IAAI,CAACO,qBAAqB,IAAI,eAAe,cAAcP,IAAI,CAACQ,mBAAmB,IAAI,eAAe,EAAG;sBAAAqJ,QAAA,eACpIzR,OAAA,CAAC1C,IAAI;wBACHqW,IAAI,EAAC,OAAO;wBACZW,KAAK,EAAES,iBAAkB;wBACzBhD,KAAK,EAAEiD,MAAO;wBACdE,IAAI,EAAEjN,YAAY,KAAK,CAAC,gBAAGjI,OAAA,CAAC7B,SAAS;0BAAA8T,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAAC3B,WAAW;0BAAA4T,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAEd,CAAC,EAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,EACPmD,aAAa,gBACZ5U,OAAA,CAAC1C,IAAI;oBACHqW,IAAI,EAAC,OAAO;oBACZuB,IAAI,eAAElV,OAAA,CAAC7B,SAAS;sBAAA8T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBkC,KAAK,EAAC,aAAa;oBACnBvC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAEFpS,OAAA,CAAC1C,IAAI;oBACHqW,IAAI,EAAC,OAAO;oBACZuB,IAAI,eAAElV,OAAA,CAAC3B,WAAW;sBAAA4T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBkC,KAAK,EAAC,iBAAiB;oBACvBvC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZpS,OAAA,CAACrD,SAAS;kBAAA8U,QAAA,EACPmD,aAAa,gBACZ5U,OAAA,CAACzC,OAAO;oBAAC0X,KAAK,EAAC,yBAAsB;oBAAAxD,QAAA,eACnCzR,OAAA,CAAC1C,IAAI;sBACH4X,IAAI,eAAElV,OAAA,CAAC7B,SAAS;wBAAA8T,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACpBkC,KAAK,EAAC,aAAa;sBACnBvC,KAAK,EAAC,SAAS;sBACf4B,IAAI,EAAC;oBAAO;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,GACRyC,cAAc,gBAChB7U,OAAA,CAACzC,OAAO;oBAAC0X,KAAK,EAAC,qCAAqC;oBAAAxD,QAAA,eAClDzR,OAAA,CAACjD,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM3H,gBAAgB,CAACnE,IAAI,CAAE;sBACtCmK,KAAK,EAAC,SAAS;sBAAAN,QAAA,eAEfzR,OAAA,CAACnC,OAAO;wBAAAoU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEVpS,OAAA,CAACzC,OAAO;oBAAC0X,KAAK,EAAEH,eAAgB;oBAAArD,QAAA,eAC9BzR,OAAA;sBAAAyR,QAAA,eACEzR,OAAA,CAACjD,UAAU;wBACT4W,IAAI,EAAC,OAAO;wBACZI,QAAQ;wBACRL,OAAO,EAAEA,CAAA,KAAMrN,YAAY,CAACyO,eAAe,EAAE,SAAS,CAAE;wBAAArD,QAAA,eAExDzR,OAAA,CAACrB,SAAS;0BAAAsT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACV;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GA3FCxK,IAAI,CAACzD,OAAO;gBAAA8N,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4FjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBlB,aAAa,CAAC5P,YAAY,CAAC,GAAG,CAAC,iBAC9BtB,OAAA,CAAC1E,GAAG;QAAC+V,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAA1D,QAAA,eAC5DzR,OAAA,CAAC9C,UAAU;UACTkY,KAAK,EAAElE,aAAa,CAAC5P,YAAY,CAAE;UACnC+T,IAAI,EAAE9S,WAAY;UAClB4Q,QAAQ,EAAEA,CAACtH,KAAK,EAAES,KAAK,KAAK9J,cAAc,CAAC8J,KAAK,CAAE;UAClDyF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMkD,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMZ,YAAY,GAAG7D,mBAAmB,CAACrP,sBAAsB,CAAC;IAEhE,IAAIA,sBAAsB,CAAC8G,MAAM,KAAK,CAAC,EAAE;MACvC,oBACEtI,OAAA,CAACxD,KAAK;QAACoH,QAAQ,EAAC,MAAM;QAAA6N,QAAA,EACnBrQ,UAAU,IAAIQ,OAAO,CAACI,SAAS,GAC5B,uDAAuD,GACvD;MAAoC;QAAAiQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IAEA,oBACEpS,OAAA,CAAAE,SAAA;MAAAuR,QAAA,gBACEzR,OAAA,CAACpD,cAAc;QAAC+X,SAAS,EAAElZ,KAAM;QAAAgW,QAAA,eAC/BzR,OAAA,CAACvD,KAAK;UAACkX,IAAI,EAAC,OAAO;UAAAlC,QAAA,gBACjBzR,OAAA,CAACnD,SAAS;YAAA4U,QAAA,eACRzR,OAAA,CAAClD,QAAQ;cAAA2U,QAAA,GACNnO,QAAQ,iBACPtD,OAAA,CAACrD,SAAS;gBAAC4Y,OAAO,EAAC,UAAU;gBAAA9D,QAAA,eAC3BzR,OAAA,CAACjD,UAAU;kBACT4W,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEtQ,aAAa,CAACkF,MAAM,KAAK9G,sBAAsB,CAAC8G,MAAM,GAAGkD,cAAc,GAAGJ,cAAe;kBAAAqG,QAAA,EAEjGrO,aAAa,CAACkF,MAAM,KAAK9G,sBAAsB,CAAC8G,MAAM,gBAAGtI,OAAA,CAAC/B,SAAS;oBAAAgU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAAC7B,SAAS;oBAAA8T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAAC3C,KAAK;kBAACqU,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpDzR,OAAA,CAACzE,UAAU;oBAAC8W,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzEpS,OAAA,CAACjD,UAAU;oBAAC4W,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtC/Q,SAAS,CAAC,oBAAoB,CAAC;sBAC/BE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA6O,QAAA,EACC/O,MAAM,KAAK,oBAAoB,GAAIE,SAAS,KAAK,KAAK,gBAAG5C,OAAA,CAACvB,cAAc;sBAAAwT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAACzB,cAAc;sBAAA0T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIpS,OAAA,CAACzB,cAAc;sBAAA0T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAAC3C,KAAK;kBAACqU,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpDzR,OAAA,CAACzE,UAAU;oBAAC8W,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/DpS,OAAA,CAACjD,UAAU;oBAAC4W,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtC/Q,SAAS,CAAC,qBAAqB,CAAC;sBAChCE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAA6O,QAAA,EACC/O,MAAM,KAAK,qBAAqB,GAAIE,SAAS,KAAK,KAAK,gBAAG5C,OAAA,CAACvB,cAAc;sBAAAwT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAACzB,cAAc;sBAAA0T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAIpS,OAAA,CAACzB,cAAc;sBAAA0T,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZpS,OAAA,CAACtD,SAAS;YAAA+U,QAAA,EACPiD,YAAY,CAACpJ,GAAG,CAAEzC,IAAI,iBACrB7I,OAAA,CAAClD,QAAQ;cAEP0Y,QAAQ,EAAEpS,aAAa,CAAC2G,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAAE;cACzDkK,KAAK;cAAAhE,QAAA,GAEJnO,QAAQ,iBACPtD,OAAA,CAACrD,SAAS;gBAAC4Y,OAAO,EAAC,UAAU;gBAAA9D,QAAA,eAC3BzR,OAAA,CAACjD,UAAU;kBACT4W,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEA,CAAA,KAAM1I,mBAAmB,CAACnC,IAAI,CAAC0C,iBAAiB,CAAE;kBAC3DwG,KAAK,EAAE3O,aAAa,CAAC2G,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAAC,GAAG,SAAS,GAAG,SAAU;kBAAAkG,QAAA,EAE7ErO,aAAa,CAAC2G,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAAC,gBAAGvL,OAAA,CAAC7B,SAAS;oBAAA8T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAACnC,OAAO;oBAAAoU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACDpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,QAAQ;kBAAAb,QAAA,EAC5C5I,IAAI,CAAC+B;gBAAkB;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAAC1C,IAAI;kBAACqW,IAAI,EAAC,OAAO;kBAACW,KAAK,EAAEzL,IAAI,CAAC1E,OAAQ;kBAACkO,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,EAAE,IAAI/I,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;cAAC;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChFpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EAAE5I,IAAI,CAAC7G,SAAS,IAAI6G,IAAI,CAACzE;gBAAY;kBAAA6N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxB5I,IAAI,CAACxE,YAAY,GAChB,CAAC,MAAM;oBACL,MAAMhC,SAAS,GAAGnB,SAAS,CAACoM,IAAI,CAACoI,CAAC,IAAIA,CAAC,CAACrR,YAAY,KAAKwE,IAAI,CAACxE,YAAY,CAAC;oBAC3E,OAAOhC,SAAS,GAAG,GAAGA,SAAS,CAACsT,IAAI,MAAMtT,SAAS,CAACuT,KAAK,EAAE,GAAG,uBAAuB;kBACvF,CAAC,EAAE,CAAC,GACD/M,IAAI,CAACgN,oBAAoB,IAAI;gBAAM;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAAAZ,QAAA,GAAE5I,IAAI,CAACvE,kBAAkB,EAAC,IAAE;gBAAA;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAAC1C,IAAI;kBACHqW,IAAI,EAAC,OAAO;kBACZW,KAAK,EAAE,GAAGzL,IAAI,CAACrE,iBAAiB,KAAM;kBACtCuN,KAAK,EAAEjH,UAAU,CAACjC,IAAI,CAACrE,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAU;kBACzE0Q,IAAI,EAAEpK,UAAU,CAACjC,IAAI,CAACrE,iBAAiB,CAAC,IAAI,GAAG,gBAAGxE,OAAA,CAAC7B,SAAS;oBAAA8T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAAC3B,WAAW;oBAAA4T,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAAC1C,IAAI;kBACHqW,IAAI,EAAC,OAAO;kBACZW,KAAK,EAAEzL,IAAI,CAAChE,gBAAgB,IAAI,UAAW;kBAC3CkN,KAAK,EAAElJ,IAAI,CAAChE,gBAAgB,KAAK,UAAU,GAAG,SAAS,GAAGgE,IAAI,CAAChE,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAG;gBAAU;kBAAAoN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZpS,OAAA,CAACrD,SAAS;gBAAA8U,QAAA,eACRzR,OAAA,CAAC3C,KAAK;kBAACqU,SAAS,EAAC,KAAK;kBAACC,OAAO,EAAE,GAAI;kBAAAF,QAAA,gBAClCzR,OAAA,CAACzC,OAAO;oBAAC0X,KAAK,EAAC,qBAAqB;oBAAAxD,QAAA,eAClCzR,OAAA,CAACjD,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM;wBACbvQ,eAAe,CAAC0F,IAAI,CAAC;wBACrB5F,aAAa,CAAC,MAAM,CAAC;wBACrBF,aAAa,CAAC,IAAI,CAAC;sBACrB,CAAE;sBAAA0O,QAAA,eAEFzR,OAAA,CAACnB,QAAQ;wBAAAoT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVpS,OAAA,CAACzC,OAAO;oBAAC0X,KAAK,EAAC,YAAY;oBAAAxD,QAAA,eACzBzR,OAAA,CAACjD,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAC7E,IAAI,CAAE;sBACvCkL,QAAQ,EAAEhQ,mBAAoB;sBAAA0N,QAAA,eAE9BzR,OAAA,CAACjB,OAAO;wBAAAkT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVpS,OAAA,CAACzC,OAAO;oBAAC0X,KAAK,EAAC,SAAS;oBAAAxD,QAAA,eACtBzR,OAAA,CAACjD,UAAU;sBACT4W,IAAI,EAAC,OAAO;sBACZ5B,KAAK,EAAC,OAAO;sBACb2B,OAAO,EAAEA,CAAA,KAAMxE,0BAA0B,CAACrG,IAAI,CAAE;sBAChDkL,QAAQ,EAAEhQ,mBAAoB;sBAAA0N,QAAA,eAE9BzR,OAAA,CAACf,UAAU;wBAAAgT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA1FPvJ,IAAI,CAAC0C,iBAAiB;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2FnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBlB,aAAa,CAAC1P,sBAAsB,CAAC,GAAG,CAAC,iBACxCxB,OAAA,CAAC1E,GAAG;QAAC+V,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAA1D,QAAA,eAC5DzR,OAAA,CAAC9C,UAAU;UACTkY,KAAK,EAAElE,aAAa,CAAC1P,sBAAsB,CAAE;UAC7C6T,IAAI,EAAE9S,WAAY;UAClB4Q,QAAQ,EAAEA,CAACtH,KAAK,EAAES,KAAK,KAAK9J,cAAc,CAAC8J,KAAK,CAAE;UAClDyF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAM0D,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI9S,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;IAEjE,oBACEhD,OAAA,CAACnE,MAAM;MAAC6H,IAAI,EAAEZ,UAAW;MAACiT,OAAO,EAAE5J,WAAY;MAAC6J,QAAQ,EAAC,IAAI;MAAC/C,SAAS;MAAAxB,QAAA,gBACrEzR,OAAA,CAAClE,WAAW;QAAA2V,QAAA,EACTzO,UAAU,KAAK,QAAQ,GAAG,sBAAsB,GAAG;MAAyB;QAAAiP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACdpS,OAAA,CAACjE,aAAa;QAAA0V,QAAA,eACZzR,OAAA,CAACtE,IAAI;UAACmX,SAAS;UAAClB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE8D,EAAE,EAAE;UAAE,CAAE;UAAA1D,QAAA,gBACxCzR,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC1D,YAAY;cACX2Z,OAAO,EAAEjV,IAAI,CAAC4H,MAAM,CAAChB,IAAI,IAAI;gBAC3B;gBACA,MAAMsO,UAAU,GAAGtO,IAAI,CAACzD,OAAO,KAAKF,QAAQ,CAACE,OAAO;gBACpD,MAAMgS,cAAc,GAAG,CAACrV,cAAc,CAACsJ,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAC1E,OAAO,KAAKyD,IAAI,CAACzD,OAAO,CAAC;gBAClF,MAAMiS,cAAc,GAAGzO,oBAAoB,CAACC,IAAI,CAAC;gBAEjD,OAAOsO,UAAU,IAAKC,cAAc,IAAIC,cAAe;cACzD,CAAC,CAAE;cACHC,cAAc,EAAG5F,MAAM,IAAK,GAAGA,MAAM,CAACtM,OAAO,MAAMsM,MAAM,CAAC1O,SAAS,EAAG;cACtEuK,KAAK,EAAEtL,IAAI,CAACsM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpJ,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC,IAAI,IAAK;cAC9DgP,QAAQ,EAAEA,CAACtH,KAAK,EAAEC,QAAQ,KAAK;gBAC7B,IAAIA,QAAQ,EAAE;kBACZS,gBAAgB,CAACT,QAAQ,CAAC;gBAC5B,CAAC,MAAM;kBACL5H,WAAW,CAACgH,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE/G,OAAO,EAAE,EAAE;oBAAEG,kBAAkB,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACzE;cACF,CAAE;cACFgS,WAAW,EAAGC,MAAM,iBAClBvW,OAAA,CAAC/D,SAAS;gBAAA,GACJsa,MAAM;gBACVjC,KAAK,EAAC,QAAQ;gBACdpB,WAAW,EAAC,0BAA0B;gBACtCsD,QAAQ;gBACRC,UAAU,EAAC;cAA6E;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CACD;cACFsE,YAAY,EAAEA,CAACC,KAAK,EAAElG,MAAM,KAAK;gBAC/B,MAAMxI,YAAY,GAAGwI,MAAM,CAACxI,YAAY,IAAI,CAAC;gBAC7C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;gBAEtC,oBACEjI,OAAA,CAAC1E,GAAG;kBAACqZ,SAAS,EAAC,IAAI;kBAAA,GAAKgC,KAAK;kBAAAlF,QAAA,eAC3BzR,OAAA,CAAC1E,GAAG;oBAAC+V,EAAE,EAAE;sBAAEmB,KAAK,EAAE;oBAAO,CAAE;oBAAAf,QAAA,eACzBzR,OAAA,CAAC3C,KAAK;sBAACqU,SAAS,EAAC,KAAK;sBAACG,cAAc,EAAC,eAAe;sBAACD,UAAU,EAAC,QAAQ;sBAAAH,QAAA,gBACvEzR,OAAA,CAAC1E,GAAG;wBAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;0BAAC8W,OAAO,EAAC,OAAO;0BAACC,UAAU,EAAC,QAAQ;0BAAAb,QAAA,EAC5ChB,MAAM,CAACtM;wBAAO;0BAAA8N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACbpS,OAAA,CAACzE,UAAU;0BAAC8W,OAAO,EAAC,SAAS;0BAACN,KAAK,EAAC,gBAAgB;0BAAAN,QAAA,GACjDhB,MAAM,CAAC1O,SAAS,EAAC,KAAG,EAAC0O,MAAM,CAACzG,mBAAmB,EAAC,UAAG,EAACyG,MAAM,CAACxG,iBAAiB;wBAAA;0BAAAgI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNpS,OAAA,CAAC3C,KAAK;wBAACqU,SAAS,EAAC,KAAK;wBAACC,OAAO,EAAE,CAAE;wBAAAF,QAAA,gBAChCzR,OAAA,CAAC1C,IAAI;0BACHqW,IAAI,EAAC,OAAO;0BACZW,KAAK,EAAE7D,MAAM,CAAC3I,mBAAoB;0BAClCiK,KAAK,EAAEtB,MAAM,CAAC3I,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;wBAAU;0BAAAmK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E,CAAC,eACFpS,OAAA,CAAC1C,IAAI;0BACHqW,IAAI,EAAC,OAAO;0BACZW,KAAK,EAAEtM,WAAW,GAAG,WAAW,GAAG,cAAe;0BAClD+J,KAAK,EAAE/J,WAAW,GAAG,SAAS,GAAG,SAAU;0BAC3CkN,IAAI,EAAElN,WAAW,gBAAGhI,OAAA,CAAC7B,SAAS;4BAAA8T,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAGpS,OAAA,CAAC3B,WAAW;4BAAA4T,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAE;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,aAAa;cACnBhI,KAAK,EAAErI,QAAQ,CAACG,YAAa;cAC7B+O,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,cAAc,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAClEkK,QAAQ;YAAA;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAACuD,QAAQ;cAAA/E,QAAA,gBAC7BzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpCpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAErI,QAAQ,CAACI,YAAa;gBAC7B8O,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,cAAc,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBAClEgI,KAAK,EAAC,aAAa;gBAAA7C,QAAA,EAElBvQ,SAAS,CAACoK,GAAG,CAAEjJ,SAAS,iBACvBrC,OAAA,CAAC3D,QAAQ;kBAA8BiQ,KAAK,EAAEjK,SAAS,CAACgC,YAAa;kBAAAoN,QAAA,GAClEpP,SAAS,CAACsT,IAAI,EAAC,KAAG,EAACtT,SAAS,CAACuT,KAAK,EAAC,GAAC,EAACvT,SAAS,CAACuU,OAAO,EAAC,SAAO,EAACvU,SAAS,CAACwU,YAAY,EAAC,GACzF;gBAAA,GAFexU,SAAS,CAACgC,YAAY;kBAAA4N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,0BAA0B;cAChCzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAErI,QAAQ,CAACK,kBAAmB;cACnC6O,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,oBAAoB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cACxEkK,QAAQ;YAAA;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAAAxB,QAAA,gBACpBzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAErI,QAAQ,CAACM,iBAAkB;gBAClC4O,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,mBAAmB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBACvEgI,KAAK,EAAC,eAAY;gBAAA7C,QAAA,gBAElBzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,IAAI;kBAAAmF,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,KAAK;kBAAAmF,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,wBAAmB;cACzBzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAErI,QAAQ,CAACO,iBAAkB;cAClC2O,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,mBAAmB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cACvEkK,QAAQ;cACRC,UAAU,EAAC;YAAmC;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAAAxB,QAAA,gBACpBzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnCpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAErI,QAAQ,CAACQ,iBAAkB;gBAClC0O,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,mBAAmB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBACvEgI,KAAK,EAAC,YAAY;gBAAA7C,QAAA,gBAElBzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,IAAI;kBAAAmF,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClCpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,KAAK;kBAAAmF,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChBzR,OAAA,CAAC5C,OAAO;cAACiU,EAAE,EAAE;gBAAE4C,EAAE,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACrBzR,OAAA,CAACzE,UAAU;gBAAC8W,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAENnO,QAAQ,CAACE,OAAO,IAAI,CAAC,MAAM;YAC1B,MAAMyD,IAAI,GAAG5G,IAAI,CAACsM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpJ,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;YAC3D,IAAI,CAACyD,IAAI,EAAE,OAAO,IAAI;YAEtB,MAAMK,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;YAC3C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;YAEtC,oBACEjI,OAAA,CAACtE,IAAI;cAACoX,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,eAChBzR,OAAA,CAACvE,KAAK;gBAAC4V,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEE,OAAO,EAAExJ,WAAW,GAAG,eAAe,GAAG;gBAAgB,CAAE;gBAAAyJ,QAAA,eAC5EzR,OAAA,CAAC3C,KAAK;kBAACqU,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,GACnDzJ,WAAW,gBAAGhI,OAAA,CAAC7B,SAAS;oBAAC4T,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGpS,OAAA,CAAC3B,WAAW;oBAAC0T,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9EpS,OAAA,CAAC1E,GAAG;oBAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;sBAAC8W,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,MAAM;sBAAAb,QAAA,EAC1CzJ,WAAW,GAAG,8BAA8B,GAAG;oBAAkC;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACbpS,OAAA,CAACzE,UAAU;sBAAC8W,OAAO,EAAC,SAAS;sBAAAZ,QAAA,GAAC,SACrB,EAACxJ,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9CA,YAAY,KAAK,CAAC,GAAG,uBAAuB,GAC5CA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9C,mBAAmB;oBAAA;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,EACZ,CAACpK,WAAW,iBACXhI,OAAA,CAAC1E,GAAG;sBAAC+V,EAAE,EAAE;wBAAE8D,EAAE,EAAE;sBAAE,CAAE;sBAAA1D,QAAA,gBACjBzR,OAAA,CAACzE,UAAU;wBAAC8W,OAAO,EAAC,SAAS;wBAACM,OAAO,EAAC,OAAO;wBAACtB,EAAE,EAAE;0BAAEE,EAAE,EAAE;wBAAE,CAAE;wBAAAE,QAAA,EAAC;sBAE7D;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbpS,OAAA,CAACxE,MAAM;wBACLmY,IAAI,EAAC,QAAQ;wBACbtB,OAAO,EAAC,WAAW;wBACnBN,KAAK,EAAC,SAAS;wBACf+E,SAAS,eAAE9W,OAAA,CAACP,QAAQ;0BAAAwS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACxBsB,OAAO,EAAEA,CAAA,KAAM1G,kCAAkC,CAACpF,IAAI,CAAE;wBACxDmM,QAAQ,EAAEhQ,mBAAoB;wBAC9BsN,EAAE,EAAE;0BACF8D,EAAE,EAAE,CAAC;0BACL7C,UAAU,EAAE,MAAM;0BAClByE,aAAa,EAAE,MAAM;0BACrBC,SAAS,EAAE,CAAC;0BACZ,SAAS,EAAE;4BACTA,SAAS,EAAE,CAAC;4BACZC,SAAS,EAAE;0BACb,CAAC;0BACDC,SAAS,EAAE,mBAAmB;0BAC9B,kBAAkB,EAAE;4BAClB,IAAI,EAAE;8BACJF,SAAS,EAAE;4BACb,CAAC;4BACD,KAAK,EAAE;8BACLA,SAAS,EAAE;4BACb,CAAC;4BACD,MAAM,EAAE;8BACNA,SAAS,EAAE;4BACb;0BACF;wBACF,CAAE;wBAAAvF,QAAA,EACH;sBAED;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAEX,CAAC,EAAE,CAAC,eAGJpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChBzR,OAAA,CAAC5C,OAAO;cAACiU,EAAE,EAAE;gBAAE4C,EAAE,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACrBzR,OAAA,CAACzE,UAAU;gBAAC8W,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,8BAA2B;cACjCzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAErI,QAAQ,CAACkT,oBAAqB;cACrChE,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,sBAAsB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAC1EmK,UAAU,EAAC;YAA6B;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,gBAAa;cACnBzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAErI,QAAQ,CAACmT,OAAQ;cACxBjE,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,SAAS,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAC7DmK,UAAU,EAAC;YAAkB;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,uBAAuB;cAC7BzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAErI,QAAQ,CAACU,cAAe;cAC/BwO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,gBAAgB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cACpEmK,UAAU,EAAC;YAAgC;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,oBAAoB;cAC1BzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAErI,QAAQ,CAACW,YAAa;cAC7BuO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,cAAc,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAClEmK,UAAU,EAAC;YAA2B;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAAC9D,WAAW;cAAC+W,SAAS;cAAAxB,QAAA,gBACpBzR,OAAA,CAAC7D,UAAU;gBAAAsV,QAAA,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzCpS,OAAA,CAAC5D,MAAM;gBACLkQ,KAAK,EAAErI,QAAQ,CAACY,gBAAiB;gBACjCsO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,kBAAkB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBACtEgI,KAAK,EAAC,kBAAkB;gBAAA7C,QAAA,gBAExBzR,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,UAAU;kBAAAmF,QAAA,eACxBzR,OAAA,CAAC3C,KAAK;oBAACqU,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpDzR,OAAA,CAAC7B,SAAS;sBAAC4T,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7BpS,OAAA,CAACzE,UAAU;sBAAAkW,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,cAAc;kBAAAmF,QAAA,eAC5BzR,OAAA,CAAC3C,KAAK;oBAACqU,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpDzR,OAAA,CAACb,SAAS;sBAAC4S,KAAK,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3BpS,OAAA,CAACzE,UAAU;sBAAAkW,QAAA,EAAC;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACXpS,OAAA,CAAC3D,QAAQ;kBAACiQ,KAAK,EAAC,eAAe;kBAAAmF,QAAA,eAC7BzR,OAAA,CAAC3C,KAAK;oBAACqU,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpDzR,OAAA,CAAC3B,WAAW;sBAAC0T,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BpS,OAAA,CAACzE,UAAU;sBAAAkW,QAAA,EAAC;oBAAa;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChBzR,OAAA,CAAC/D,SAAS;cACRgX,SAAS;cACTqB,KAAK,EAAC,MAAM;cACZ+C,SAAS;cACTxH,IAAI,EAAE,CAAE;cACRvD,KAAK,EAAErI,QAAQ,CAACS,IAAK;cACrByO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,MAAM,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAC1D4G,WAAW,EAAC;YAAkF;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBpS,OAAA,CAAChE,aAAa;QAAAyV,QAAA,gBACZzR,OAAA,CAACxE,MAAM;UAACkY,OAAO,EAAEvH,WAAY;UAAAsF,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9CpS,OAAA,CAACxE,MAAM;UACLkY,OAAO,EAAErG,0BAA2B;UACpCgF,OAAO,EAAC,WAAW;UACnB0B,QAAQ,EAAErT,OAAO,IAAI,CAACuD,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAkB;UAAAiN,QAAA,EAEzHzO,UAAU,KAAK,QAAQ,GAAG,qBAAqB,GAAG;QAAiB;UAAAiP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMkF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAItU,UAAU,KAAK,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEvD,oBACElD,OAAA,CAACnE,MAAM;MAAC6H,IAAI,EAAEZ,UAAW;MAACiT,OAAO,EAAE5J,WAAY;MAAC6J,QAAQ,EAAC,IAAI;MAAC/C,SAAS;MAAAxB,QAAA,gBACrEzR,OAAA,CAAClE,WAAW;QAAA2V,QAAA,GAAC,4BACe,EAACvO,YAAY,CAAC0H,kBAAkB;MAAA;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACdpS,OAAA,CAACjE,aAAa;QAAA0V,QAAA,eACZzR,OAAA,CAACtE,IAAI;UAACmX,SAAS;UAAClB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE8D,EAAE,EAAE;UAAE,CAAE;UAAA1D,QAAA,gBACxCzR,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAACrE,IAAI;cAAC0W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBzR,OAAA,CAACpE,WAAW;gBAAA6V,QAAA,gBACVzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,IAAI;kBAACkF,YAAY;kBAAA9F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpS,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,WACxC,eAAAzR,OAAA;oBAAAyR,QAAA,EAASvO,YAAY,CAACiB;kBAAO;oBAAA8N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACbpS,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,sBAC7B,eAAAzR,OAAA;oBAAAyR,QAAA,GAASvO,YAAY,CAACoB,kBAAkB,EAAC,IAAE;kBAAA;oBAAA2N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvBzR,OAAA,CAACrE,IAAI;cAAC0W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBzR,OAAA,CAACpE,WAAW;gBAAA6V,QAAA,gBACVzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,IAAI;kBAACkF,YAAY;kBAAA9F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpS,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,UACzC,eAAAzR,OAAA;oBAAAyR,QAAA,EAASvO,YAAY,CAAC0H;kBAAkB;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACbpS,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,QAC3C,eAAAzR,OAAA;oBAAAyR,QAAA,EAAS,IAAI/I,IAAI,CAACxF,YAAY,CAAC4F,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACbpS,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,aACtC,eAAAzR,OAAA;oBAAAyR,QAAA,EAASvO,YAAY,CAAClB,SAAS,IAAIkB,YAAY,CAACkB;kBAAY;oBAAA6N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPpS,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChBzR,OAAA,CAACrE,IAAI;cAAC0W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBzR,OAAA,CAACpE,WAAW;gBAAA6V,QAAA,gBACVzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,IAAI;kBAACkF,YAAY;kBAAA9F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpS,OAAA,CAACtE,IAAI;kBAACmX,SAAS;kBAAClB,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACzBzR,OAAA,CAACtE,IAAI;oBAACoX,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAtB,QAAA,gBACfzR,OAAA,CAACzE,UAAU;sBAAC8W,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbpS,OAAA,CAAC1C,IAAI;sBACHqW,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAEpR,YAAY,CAACqB,iBAAkB;sBACtCwN,KAAK,EAAE7O,YAAY,CAACqB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA0N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPpS,OAAA,CAACtE,IAAI;oBAACoX,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAtB,QAAA,gBACfzR,OAAA,CAACzE,UAAU;sBAAC8W,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbpS,OAAA,CAAC1C,IAAI;sBACHqW,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAE,GAAGpR,YAAY,CAACsB,iBAAiB,KAAM;sBAC9CuN,KAAK,EAAEjH,UAAU,CAAC5H,YAAY,CAACsB,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;oBAAU;sBAAAyN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACPpS,OAAA,CAACtE,IAAI;oBAACoX,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAtB,QAAA,gBACfzR,OAAA,CAACzE,UAAU;sBAAC8W,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbpS,OAAA,CAAC1C,IAAI;sBACHqW,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAEpR,YAAY,CAACuB,iBAAkB;sBACtCsN,KAAK,EAAE7O,YAAY,CAACuB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAAwN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAENlP,YAAY,CAACwB,IAAI,iBAChB1E,OAAA,CAACtE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChBzR,OAAA,CAACrE,IAAI;cAAC0W,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBzR,OAAA,CAACpE,WAAW;gBAAA6V,QAAA,gBACVzR,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,IAAI;kBAACkF,YAAY;kBAAA9F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbpS,OAAA,CAACzE,UAAU;kBAAC8W,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBvO,YAAY,CAACwB;gBAAI;kBAAAuN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBpS,OAAA,CAAChE,aAAa;QAAAyV,QAAA,gBACZzR,OAAA,CAACxE,MAAM;UAACkY,OAAO,EAAEvH,WAAY;UAAAsF,QAAA,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CpS,OAAA,CAACxE,MAAM;UACLkY,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAACxK,YAAY,CAAE;UAC/CmP,OAAO,EAAC,WAAW;UACnB0B,QAAQ,EAAErT,OAAQ;UAAA+Q,QAAA,EACnB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;EAID,oBACEpS,OAAA,CAACrC,SAAS;IAACqY,QAAQ,EAAC,IAAI;IAAC3E,EAAE,EAAE;MAAEmG,EAAE,EAAE;IAAE,CAAE;IAAA/F,QAAA,GAEpCL,eAAe,CAAC,CAAC,EAGjB,CAAC1Q,OAAO,IAAIqD,mBAAmB,kBAC9B/D,OAAA,CAAC1E,GAAG;MAAC+V,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACjBzR,OAAA,CAACxC,cAAc;QAAAyU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjBvO,QAAQ,GAAG,CAAC,iBACX7D,OAAA,CAACzE,UAAU;QAAC8W,OAAO,EAAC,SAAS;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAE8D,EAAE,EAAE;QAAE,CAAE;QAAA1D,QAAA,GAAC,iBACnD,EAAC5N,QAAQ,EAAC,GAC3B;MAAA;QAAAoO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDpS,OAAA,CAACvE,KAAK;MAAC4V,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,eACnBzR,OAAA,CAAChD,IAAI;QACHsP,KAAK,EAAE1L,SAAU;QACjBuS,QAAQ,EAAEvH,eAAgB;QAC1B6L,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnBrF,OAAO,EAAC,WAAW;QAAAZ,QAAA,gBAEnBzR,OAAA,CAAC/C,GAAG;UACFqX,KAAK,eACHtU,OAAA,CAAC1E,GAAG;YAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;cAAC8W,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpS,OAAA,CAACzE,UAAU;cAAC8W,OAAO,EAAC,SAAS;cAACN,KAAK,EAAC,gBAAgB;cAAAN,QAAA,GACjDnQ,YAAY,CAACgH,MAAM,EAAC,cACvB;YAAA;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFpS,OAAA,CAAC/C,GAAG;UACFqX,KAAK,eACHtU,OAAA,CAAC1E,GAAG;YAAAmW,QAAA,gBACFzR,OAAA,CAACzE,UAAU;cAAC8W,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpS,OAAA,CAACzE,UAAU;cAAC8W,OAAO,EAAC,SAAS;cAACN,KAAK,EAAC,gBAAgB;cAAAN,QAAA,GACjDjQ,sBAAsB,CAAC8G,MAAM,EAAC,iBACjC;YAAA;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGPQ,sBAAsB,CAAC,CAAC,EAGxB,CAAClS,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAI6T,eAAe,CAAC,CAAC,EAChD,CAAC/T,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAI0U,yBAAyB,CAAC,CAAC,EAG1DQ,0BAA0B,CAAC,CAAC,EAC5BwB,gBAAgB,CAAC,CAAC,eAGnBtX,OAAA,CAACtC,QAAQ;MACPgG,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBiU,gBAAgB,EAAE,IAAK;MACvB5B,OAAO,EAAEzM,aAAc;MACvBsO,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAArG,QAAA,eAE1DzR,OAAA,CAACxD,KAAK;QAACuZ,OAAO,EAAEzM,aAAc;QAAC1F,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAACyN,EAAE,EAAE;UAAEmB,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,EAC/EjO,QAAQ,CAACG;MAAO;QAAAsO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGF,CAAC;AAEhB,CAAC,kCAAC;AAAC2F,GAAA,GAjlEG5X,0BAA0B;AAmlEhC,eAAeA,0BAA0B;AAAC,IAAAE,EAAA,EAAA0X,GAAA;AAAAC,YAAA,CAAA3X,EAAA;AAAA2X,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
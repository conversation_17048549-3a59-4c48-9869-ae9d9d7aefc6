from typing import Any, List
from datetime import datetime
import secrets
import string
import bcrypt

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.cavo import Cavo
from backend.models.parco_cavi import ParcoCavo
from backend.models.strumento_certificato import StrumentoCertificato
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.schemas.cantiere import (
    CantiereCreate, CantiereInDB, CantiereUpdate,
    PasswordVerifyRequest, PasswordChangeRequest,
    PasswordVerifyResponse, PasswordChangeResponse, PasswordViewResponse
)
from backend.core.security import get_current_active_user, get_password_hash, verify_cantiere_password
from backend.core.password_encryption import encrypt_password, decrypt_password, generate_random_delay

router = APIRouter()

def genera_codice_univoco_targa() -> str:
    """
    Genera un codice univoco tipo targa auto (es. AB123CD).

    Returns:
        str: Codice univoco generato
    """
    # Genera due lettere maiuscole
    lettere_inizio = ''.join(secrets.choice(string.ascii_uppercase) for _ in range(2))
    # Genera tre numeri
    numeri = ''.join(secrets.choice(string.digits) for _ in range(3))
    # Genera due lettere maiuscole
    lettere_fine = ''.join(secrets.choice(string.ascii_uppercase) for _ in range(2))

    # Combina le parti per formare il codice univoco
    codice_univoco = f"{lettere_inizio}{numeri}{lettere_fine}"

    return codice_univoco

@router.get("/", response_model=List[CantiereInDB])
def get_cantieri(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista dei cantieri dell'utente corrente.

    Args:
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[CantiereInDB]: Lista dei cantieri dell'utente
    """
    # Ottieni i dati del token per verificare se l'utente sta impersonando un altro utente
    token_data = getattr(current_user, "token_data", None)
    is_impersonated = token_data and getattr(token_data, "is_impersonated", False)
    impersonated_id = token_data and getattr(token_data, "impersonated_id", None)

    # Se l'utente è un amministratore che sta impersonando un utente, restituisci i cantieri dell'utente impersonato
    if current_user.ruolo == "owner" and is_impersonated and impersonated_id:
        cantieri = db.query(Cantiere).filter(Cantiere.id_utente == impersonated_id).all()
    # Se l'utente è un amministratore (non impersonando), restituisci tutti i cantieri
    elif current_user.ruolo == "owner":
        cantieri = db.query(Cantiere).all()
    # Altrimenti, restituisci solo i cantieri dell'utente
    else:
        cantieri = db.query(Cantiere).filter(Cantiere.id_utente == current_user.id_utente).all()

    return cantieri

@router.get("/user/{user_id}", response_model=List[CantiereInDB])
def get_user_cantieri(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista dei cantieri di un utente specifico (solo per amministratori).

    Args:
        user_id: ID dell'utente
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[CantiereInDB]: Lista dei cantieri dell'utente

    Raises:
        HTTPException: Se l'utente corrente non è un amministratore
    """
    # Verifica che l'utente corrente sia un amministratore
    if current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Solo gli amministratori possono visualizzare i cantieri di altri utenti"
        )

    # Verifica che l'utente esista
    user = db.query(User).filter(User.id_utente == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Utente con ID {user_id} non trovato"
        )

    # Ottieni i cantieri dell'utente
    cantieri = db.query(Cantiere).filter(Cantiere.id_utente == user_id).all()

    return cantieri

@router.post("/", response_model=CantiereInDB)
def create_cantiere(
    cantiere_in: CantiereCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea un nuovo cantiere.

    Args:
        cantiere_in: Dati del nuovo cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CantiereInDB: Cantiere creato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari
    """
    # Verifica che l'utente non sia un amministratore (solo gli utenti standard possono creare cantieri)
    if current_user.ruolo == "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Gli amministratori non possono creare cantieri"
        )

    # Genera un codice univoco tipo targa auto
    codice_univoco = genera_codice_univoco_targa()

    # Verifica che il codice univoco non sia già presente nel database
    while db.query(Cantiere).filter(Cantiere.codice_univoco == codice_univoco).first():
        codice_univoco = genera_codice_univoco_targa()

    # Cripta la password per permettere il recupero futuro
    encrypted_password = encrypt_password(cantiere_in.password_cantiere)

    # Crea il nuovo cantiere
    cantiere = Cantiere(
        commessa=cantiere_in.commessa,
        descrizione=cantiere_in.descrizione,
        nome_cliente=cantiere_in.nome_cliente,
        indirizzo_cantiere=cantiere_in.indirizzo_cantiere,
        citta_cantiere=cantiere_in.citta_cantiere,
        nazione_cantiere=cantiere_in.nazione_cantiere,
        riferimenti_normativi=cantiere_in.riferimenti_normativi,
        documentazione_progetto=cantiere_in.documentazione_progetto,
        data_creazione=datetime.now(),
        password_cantiere=get_password_hash(cantiere_in.password_cantiere),
        password_cantiere_encrypted=encrypted_password,
        id_utente=current_user.id_utente,
        codice_univoco=codice_univoco
    )

    # Salva il cantiere nel database
    db.add(cantiere)
    db.commit()
    db.refresh(cantiere)

    return cantiere

@router.get("/{cantiere_id}", response_model=CantiereInDB)
def get_cantiere(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene i dettagli di un cantiere specifico.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CantiereInDB: Dettagli del cantiere

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    # Ottieni il cantiere dal database
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()

    # Verifica che il cantiere esista
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    return cantiere

@router.put("/{cantiere_id}", response_model=CantiereInDB)
def update_cantiere(
    cantiere_id: int,
    cantiere_in: CantiereUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna un cantiere esistente.

    Args:
        cantiere_id: ID del cantiere da aggiornare
        cantiere_in: Dati aggiornati del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CantiereInDB: Cantiere aggiornato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    # Ottieni il cantiere dal database
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()

    # Verifica che il cantiere esista
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per modificare questo cantiere"
        )

    # Aggiorna i campi del cantiere
    if cantiere_in.commessa is not None:
        cantiere.commessa = cantiere_in.commessa
    if cantiere_in.descrizione is not None:
        cantiere.descrizione = cantiere_in.descrizione
    if cantiere_in.nome_cliente is not None:
        cantiere.nome_cliente = cantiere_in.nome_cliente
    if cantiere_in.indirizzo_cantiere is not None:
        cantiere.indirizzo_cantiere = cantiere_in.indirizzo_cantiere
    if cantiere_in.citta_cantiere is not None:
        cantiere.citta_cantiere = cantiere_in.citta_cantiere
    if cantiere_in.nazione_cantiere is not None:
        cantiere.nazione_cantiere = cantiere_in.nazione_cantiere
    if cantiere_in.riferimenti_normativi is not None:
        cantiere.riferimenti_normativi = cantiere_in.riferimenti_normativi
    if cantiere_in.documentazione_progetto is not None:
        cantiere.documentazione_progetto = cantiere_in.documentazione_progetto
    if cantiere_in.password_cantiere is not None:
        cantiere.password_cantiere = get_password_hash(cantiere_in.password_cantiere)
        # Cripta anche la nuova password per permettere il recupero futuro
        cantiere.password_cantiere_encrypted = encrypt_password(cantiere_in.password_cantiere)

    # Salva le modifiche nel database
    db.commit()
    db.refresh(cantiere)

    return cantiere

@router.delete("/{cantiere_id}", response_model=dict)
def delete_cantiere(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina un cantiere e tutti i dati correlati.

    Args:
        cantiere_id: ID del cantiere da eliminare
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Messaggio di conferma

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    # Ottieni il cantiere dal database
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()

    # Verifica che il cantiere esista
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per eliminare questo cantiere"
        )

    try:

        # 1. Elimina tutte le certificazioni dei cavi del cantiere
        db.query(CertificazioneCavo).filter(CertificazioneCavo.id_cantiere == cantiere_id).delete(synchronize_session=False)

        # 2. Elimina tutti i cavi del cantiere
        db.query(Cavo).filter(Cavo.id_cantiere == cantiere_id).delete(synchronize_session=False)

        # 3. Elimina tutte le bobine del cantiere
        db.query(ParcoCavo).filter(ParcoCavo.id_cantiere == cantiere_id).delete(synchronize_session=False)

        # 4. Elimina tutti gli strumenti certificati del cantiere
        db.query(StrumentoCertificato).filter(StrumentoCertificato.id_cantiere == cantiere_id).delete(synchronize_session=False)

        # 5. Elimina il cantiere
        db.delete(cantiere)

        # Commit delle modifiche
        db.commit()

        return {"message": f"Cantiere {cantiere_id} eliminato con successo"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'eliminazione del cantiere: {str(e)}"
        )

@router.post("/{cantiere_id}/verify-password", response_model=PasswordVerifyResponse)
def verify_cantiere_password_endpoint(
    cantiere_id: int,
    password_request: PasswordVerifyRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Verifica la password del cantiere e la restituisce se corretta.

    Args:
        cantiere_id: ID del cantiere
        password_request: Richiesta con password attuale
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        PasswordVerifyResponse: Risultato della verifica con password se corretta

    Raises:
        HTTPException: Se l'utente non ha i permessi o il cantiere non esiste
    """
    # Ottieni il cantiere dal database
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()

    # Verifica che il cantiere esista
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Verifica la password
    password_corretta = verify_cantiere_password(
        password_request.password_attuale,
        cantiere.password_cantiere
    )

    if password_corretta:
        # Se la password è corretta, restituisci la password in chiaro
        return PasswordVerifyResponse(
            password_corretta=True,
            password_cantiere=password_request.password_attuale
        )
    else:
        return PasswordVerifyResponse(
            password_corretta=False,
            password_cantiere=None
        )

@router.post("/{cantiere_id}/change-password", response_model=PasswordChangeResponse)
def change_cantiere_password_endpoint(
    cantiere_id: int,
    password_request: PasswordChangeRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Cambia la password del cantiere dopo aver verificato quella attuale.

    Args:
        cantiere_id: ID del cantiere
        password_request: Richiesta con password attuale e nuova
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        PasswordChangeResponse: Risultato del cambio password

    Raises:
        HTTPException: Se l'utente non ha i permessi o il cantiere non esiste
    """
    # Ottieni il cantiere dal database
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()

    # Verifica che il cantiere esista
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per modificare questo cantiere"
        )

    # Verifica che le nuove password coincidano
    if password_request.password_nuova != password_request.conferma_password:
        return PasswordChangeResponse(
            success=False,
            message="Le nuove password non coincidono"
        )

    # Verifica che la nuova password non sia vuota
    if not password_request.password_nuova or len(password_request.password_nuova.strip()) == 0:
        return PasswordChangeResponse(
            success=False,
            message="La nuova password non può essere vuota"
        )

    # Verifica la password attuale
    password_corretta = verify_cantiere_password(
        password_request.password_attuale,
        cantiere.password_cantiere
    )

    if not password_corretta:
        return PasswordChangeResponse(
            success=False,
            message="Password attuale non corretta"
        )

    try:
        # Aggiorna la password del cantiere
        cantiere.password_cantiere = get_password_hash(password_request.password_nuova)
        # Cripta anche la nuova password per permettere il recupero futuro
        cantiere.password_cantiere_encrypted = encrypt_password(password_request.password_nuova)

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cantiere)

        return PasswordChangeResponse(
            success=True,
            message="Password cambiata con successo"
        )
    except Exception as e:
        db.rollback()
        return PasswordChangeResponse(
            success=False,
            message=f"Errore durante il cambio password: {str(e)}"
        )

@router.get("/{cantiere_id}/view-password", response_model=PasswordViewResponse)
def view_cantiere_password_direct(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Visualizza direttamente la password del cantiere senza verifica.
    Utilizzato per il recupero password quando l'utente l'ha dimenticata.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        PasswordViewResponse: Password del cantiere

    Raises:
        HTTPException: Se l'utente non ha i permessi o il cantiere non esiste
    """
    # Ottieni il cantiere dal database
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()

    # Verifica che il cantiere esista
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Implementa delay casuale per sicurezza anti-bot
    import time
    import random
    delay = random.uniform(2.0, 5.0)
    time.sleep(delay)

    # Prova a decriptare la password se è disponibile la versione criptata
    if cantiere.password_cantiere_encrypted:
        try:
            decrypted_password = decrypt_password(cantiere.password_cantiere_encrypted)
            if decrypted_password:
                return PasswordViewResponse(
                    password_cantiere=decrypted_password
                )
        except Exception as e:
            print(f"Errore nella decriptazione della password: {e}")

    # Se la password è hashata (inizia con $2), non possiamo recuperare quella originale
    if isinstance(cantiere.password_cantiere, str) and cantiere.password_cantiere.startswith('$2'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="La password è hashata e non può essere recuperata. Utilizza la funzione di cambio password."
        )

    # Se la password è in chiaro (caso legacy), la restituiamo
    return PasswordViewResponse(
        password_cantiere=str(cantiere.password_cantiere)
    )

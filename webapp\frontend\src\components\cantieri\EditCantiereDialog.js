import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  IconButton,
  Divider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Edit as EditIcon,
  Lock as LockIcon,
  Construction as ConstructionIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import cantieriService from '../../services/cantieriService';
import PasswordManagementDialog from './PasswordManagementDialog';

/**
 * Dialog per la modifica dei dati del cantiere
 * Include accesso rapido alla gestione password
 */
const EditCantiereDialog = ({
  open,
  onClose,
  cantiere,
  onCantiereUpdated = null
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  
  const [formData, setFormData] = useState({
    commessa: '',
    descrizione: '',
    nome_cliente: '',
    indirizzo_cantiere: '',
    citta_cantiere: '',
    nazione_cantiere: '',
    riferimenti_normativi: '',
    documentazione_progetto: ''
  });

  // Inizializza i dati del form quando si apre il dialog
  useEffect(() => {
    if (open && cantiere) {
      setFormData({
        commessa: cantiere.commessa || cantiere.nome || '',
        descrizione: cantiere.descrizione || '',
        nome_cliente: cantiere.nome_cliente || '',
        indirizzo_cantiere: cantiere.indirizzo_cantiere || '',
        citta_cantiere: cantiere.citta_cantiere || '',
        nazione_cantiere: cantiere.nazione_cantiere || 'Italia',
        riferimenti_normativi: cantiere.riferimenti_normativi || '',
        documentazione_progetto: cantiere.documentazione_progetto || ''
      });
      setError('');
      setSuccess('');
    }
  }, [open, cantiere]);

  // Gestisce i cambiamenti nei campi del form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Gestisce il salvataggio delle modifiche
  const handleSave = async () => {
    // Validazioni
    if (!formData.nome.trim()) {
      setError('Il nome del cantiere è obbligatorio');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const updatedCantiere = await cantieriService.updateCantiere(
        cantiere.id_cantiere,
        {
          commessa: formData.commessa.trim(),
          descrizione: formData.descrizione.trim() || null,
          nome_cliente: formData.nome_cliente.trim() || null,
          indirizzo_cantiere: formData.indirizzo_cantiere.trim() || null,
          citta_cantiere: formData.citta_cantiere.trim() || null,
          nazione_cantiere: formData.nazione_cantiere.trim() || null,
          riferimenti_normativi: formData.riferimenti_normativi.trim() || null,
          documentazione_progetto: formData.documentazione_progetto.trim() || null
        }
      );
      
      setSuccess('Cantiere aggiornato con successo!');
      
      // Notifica il componente padre
      if (onCantiereUpdated) {
        onCantiereUpdated(updatedCantiere);
      }
      
      // Chiudi il dialog dopo un breve delay
      setTimeout(() => {
        handleClose();
      }, 1500);
      
    } catch (err) {
      console.error('Errore nell\'aggiornamento del cantiere:', err);
      setError(err.detail || 'Errore nell\'aggiornamento del cantiere');
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'apertura del dialog password
  const handleOpenPasswordDialog = () => {
    setShowPasswordDialog(true);
  };

  // Gestisce la chiusura del dialog password
  const handleClosePasswordDialog = () => {
    setShowPasswordDialog(false);
  };

  // Gestisce la chiusura del dialog principale
  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  // Gestisce il cambio password completato
  const handlePasswordChanged = () => {
    setSuccess('Password cambiata con successo!');
    // Il dialog password si chiuderà automaticamente
  };

  if (!cantiere) return null;

  return (
    <>
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ConstructionIcon />
            <Typography variant="h6">
              Modifica Cantiere
            </Typography>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              <strong>Codice Univoco:</strong> {cantiere.codice_univoco}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Data Creazione:</strong> {new Date(cantiere.data_creazione).toLocaleDateString('it-IT')}
            </Typography>
          </Box>

          <Divider sx={{ my: 2 }} />
          
          {/* Sezione Informazioni Generali */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{
              fontWeight: 'bold',
              color: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <DescriptionIcon fontSize="small" />
              Informazioni Generali
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome Commessa"
                  name="commessa"
                  value={formData.commessa}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Descrizione"
                  name="descrizione"
                  value={formData.descrizione}
                  onChange={handleInputChange}
                  multiline
                  rows={2}
                  disabled={loading}
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Sezione Cliente e Localizzazione */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{
              fontWeight: 'bold',
              color: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <BusinessIcon fontSize="small" />
              Cliente e Localizzazione
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Nome Cliente"
                  name="nome_cliente"
                  value={formData.nome_cliente}
                  onChange={handleInputChange}
                  disabled={loading}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Nazione</InputLabel>
                  <Select
                    name="nazione_cantiere"
                    value={formData.nazione_cantiere}
                    onChange={handleInputChange}
                    disabled={loading}
                    label="Nazione"
                  >
                    <MenuItem value="Italia">Italia</MenuItem>
                    <MenuItem value="Francia">Francia</MenuItem>
                    <MenuItem value="Germania">Germania</MenuItem>
                    <MenuItem value="Spagna">Spagna</MenuItem>
                    <MenuItem value="Regno Unito">Regno Unito</MenuItem>
                    <MenuItem value="Svizzera">Svizzera</MenuItem>
                    <MenuItem value="Austria">Austria</MenuItem>
                    <MenuItem value="Altro">Altro</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Città"
                  name="citta_cantiere"
                  value={formData.citta_cantiere}
                  onChange={handleInputChange}
                  disabled={loading}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Indirizzo Cantiere"
                  name="indirizzo_cantiere"
                  value={formData.indirizzo_cantiere}
                  onChange={handleInputChange}
                  disabled={loading}
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Sezione Normative e Documentazione */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{
              fontWeight: 'bold',
              color: 'primary.main'
            }}>
              Normative e Documentazione
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Riferimenti Normativi"
                  name="riferimenti_normativi"
                  value={formData.riferimenti_normativi}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Es. CEI 64-8 Parte 6; IEC 60364-6"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Documentazione Progetto"
                  name="documentazione_progetto"
                  value={formData.documentazione_progetto}
                  onChange={handleInputChange}
                  multiline
                  rows={2}
                  disabled={loading}
                  placeholder="Es. Schemi elettrici e layout di progetto"
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Sezione Gestione Password */}
          <Box sx={{ 
            p: 2, 
            bgcolor: 'background.default', 
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'divider'
          }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LockIcon fontSize="small" />
              Gestione Password
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Visualizza o modifica la password del cantiere
            </Typography>
            <Button
              variant="outlined"
              startIcon={<LockIcon />}
              onClick={handleOpenPasswordDialog}
              fullWidth
            >
              Gestisci Password
            </Button>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button 
            onClick={handleSave} 
            variant="contained"
            disabled={loading || !formData.commessa.trim()}
            startIcon={<EditIcon />}
          >
            {loading ? 'Salvataggio...' : 'Salva Modifiche'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per la gestione password */}
      <PasswordManagementDialog
        open={showPasswordDialog}
        onClose={handleClosePasswordDialog}
        cantiere={cantiere}
        onPasswordChanged={handlePasswordChanged}
      />
    </>
  );
};

export default EditCantiereDialog;

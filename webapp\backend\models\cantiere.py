from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from backend.database import Base

class Cantiere(Base):
    """
    Modello SQLAlchemy per la tabella cantieri.
    Corrisponde alla tabella cantieri nel database esistente.
    """
    __tablename__ = "cantieri"

    id_cantiere = Column(Integer, primary_key=True, index=True)
    commessa = Column(String, nullable=False)  # Rinominato da 'nome'
    descrizione = Column(String, nullable=True)

    # Informazioni cliente e localizzazione
    nome_cliente = Column(String, nullable=True)
    indirizzo_cantiere = Column(String, nullable=True)
    citta_cantiere = Column(String, nullable=True)
    nazione_cantiere = Column(String, nullable=True)

    data_creazione = Column(DateTime, nullable=False)
    password_cantiere = Column(String, nullable=False)  # Password hashata per autenticazione
    password_cantiere_encrypted = Column(String, nullable=True)  # Password criptata per recupero
    id_utente = Column(Integer, ForeignKey("utenti.id_utente"), nullable=False)
    codice_univoco = Column(String, nullable=False, unique=True)

    # Informazioni normative e documentazione
    riferimenti_normativi = Column(String, nullable=True)  # Normative e specifiche applicate
    documentazione_progetto = Column(String, nullable=True)  # Riferimenti a schemi e layout

    # Relazioni
    utente = relationship("User", back_populates="cantieri")
    cavi = relationship("Cavo", back_populates="cantiere")

# Migrazione Cantieri - Localizzazione Geografica

## Panoramica

Questo script migra la struttura della tabella `cantieri` per supportare la localizzazione geografica dei cantieri, necessaria per il recupero automatico di temperatura e umidità durante la certificazione.

## Modifiche alla struttura

### Campi rinominati:
- `nome` → `commessa`

### Nuovi campi aggiunti:
- `nome_cliente` (TEXT) - Nome del cliente del cantiere
- `indirizzo_cantiere` (TEXT) - Indirizzo completo del cantiere  
- `citta_cantiere` (TEXT) - Città del cantiere
- `nazione_cantiere` (TEXT) - Nazione del cantiere

### Campi rimossi:
- `progetto_commessa` (duplicato con commessa)
- `codice_progetto` (duplicato con codice_univoco)

### Campi mantenuti:
- `riferimenti_normativi` 
- `documentazione_progetto`

## Struttura finale

```sql
CREATE TABLE cantieri (
    id_cantiere SERIAL PRIMARY KEY,
    commessa TEXT NOT NULL,                    -- Rinominato da 'nome'
    descrizione TEXT,
    nome_cliente TEXT,                         -- NUOVO
    indirizzo_cantiere TEXT,                   -- NUOVO  
    citta_cantiere TEXT,                       -- NUOVO
    nazione_cantiere TEXT,                     -- NUOVO
    data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    password_cantiere TEXT NOT NULL,
    id_utente INTEGER NOT NULL,
    codice_univoco TEXT UNIQUE NOT NULL,
    riferimenti_normativi TEXT,
    documentazione_progetto TEXT,
    FOREIGN KEY (id_utente) REFERENCES Utenti(id_utente)
);
```

## Come eseguire la migrazione

### 1. Backup del database
```bash
# Backup completo del database
pg_dump -h localhost -U postgres -d cantieri > backup_pre_migrazione_$(date +%Y%m%d_%H%M%S).sql
```

### 2. Esecuzione dello script
```bash
cd scripts
python migrate_cantieri_localizzazione.py
```

### 3. Verifica della migrazione
Lo script include verifiche automatiche, ma puoi controllare manualmente:

```sql
-- Verifica struttura tabella
\d cantieri

-- Verifica dati
SELECT id_cantiere, commessa, nome_cliente, citta_cantiere, nazione_cantiere 
FROM cantieri 
LIMIT 5;
```

## Valori di default

Per i cantieri esistenti, lo script imposta automaticamente:
- `nome_cliente`: "Cliente da definire"
- `indirizzo_cantiere`: "Indirizzo da definire"  
- `citta_cantiere`: "Città da definire"
- `nazione_cantiere`: "Italia"
- `riferimenti_normativi`: "CEI 64-8 Parte 6; IEC 60364-6"
- `documentazione_progetto`: "Schemi elettrici e layout di progetto"

## Rollback

In caso di problemi, puoi ripristinare dal backup:

```bash
# Ripristina dal backup
psql -h localhost -U postgres -d cantieri < backup_pre_migrazione_YYYYMMDD_HHMMSS.sql
```

## Aggiornamenti necessari al codice

Dopo la migrazione, aggiorna:

1. **Frontend**: Modifica i form per includere i nuovi campi
2. **API**: Aggiorna gli endpoint per gestire i nuovi campi
3. **Validazione**: Aggiorna le regole di validazione

## Prossimi passi

Dopo questa migrazione sarà possibile implementare:

1. **Servizio meteorologico**: Recupero automatico temperatura/umidità
2. **Geocoding**: Conversione automatica indirizzo → coordinate
3. **Mappe**: Visualizzazione cantieri su mappa
4. **Reportistica geografica**: Report per area geografica

## Log della migrazione

Lo script genera un log dettagliato in:
- `migrate_cantieri_localizzazione.log`

## Supporto

Per problemi o domande sulla migrazione, controlla:
1. Il file di log generato
2. La tabella di backup `cantieri_backup_pre_localizzazione`
3. I messaggi di errore nel terminale

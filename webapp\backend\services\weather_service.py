"""
Servizio per il recupero di dati meteorologici basato sulla localizzazione del cantiere.
Utilizza OpenWeatherMap API per ottenere temperatura e umidità attuali.
"""

import os
import requests
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

# Configurazione logging
logger = logging.getLogger(__name__)

class WeatherService:
    """Servizio per il recupero di dati meteorologici."""
    
    def __init__(self):
        # API Key di OpenWeatherMap (gratuita fino a 1000 chiamate/giorno)
        self.api_key = os.environ.get('OPENWEATHER_API_KEY', 'demo_key')
        self.base_url = "http://api.openweathermap.org/data/2.5/weather"
        self.geocoding_url = "http://api.openweathermap.org/geo/1.0/direct"
        
        # Cache per evitare chiamate eccessive (cache per 30 minuti)
        self._cache = {}
        self._cache_duration = timedelta(minutes=30)
    
    def _get_cache_key(self, city: str, country: str) -> str:
        """Genera una chiave per la cache."""
        return f"{city.lower()}_{country.lower()}"
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Verifica se l'entry della cache è ancora valida."""
        if not cache_entry:
            return False
        
        cache_time = cache_entry.get('timestamp')
        if not cache_time:
            return False
        
        return datetime.now() - cache_time < self._cache_duration
    
    def get_coordinates(self, city: str, country: str) -> Optional[Dict[str, float]]:
        """
        Ottiene le coordinate geografiche di una città.
        
        Args:
            city: Nome della città
            country: Nome della nazione
            
        Returns:
            Dict con 'lat' e 'lon' o None se non trovato
        """
        try:
            # Parametri per la geocoding API
            params = {
                'q': f"{city},{country}",
                'limit': 1,
                'appid': self.api_key
            }
            
            logger.info(f"🌍 Geocoding per {city}, {country}")
            response = requests.get(self.geocoding_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data and len(data) > 0:
                location = data[0]
                coordinates = {
                    'lat': location['lat'],
                    'lon': location['lon']
                }
                logger.info(f"✅ Coordinate trovate: {coordinates}")
                return coordinates
            else:
                logger.warning(f"⚠️ Nessuna coordinata trovata per {city}, {country}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"❌ Errore nella geocoding: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Errore imprevisto nella geocoding: {e}")
            return None
    
    def get_weather_by_coordinates(self, lat: float, lon: float) -> Optional[Dict[str, Any]]:
        """
        Ottiene i dati meteorologici per coordinate specifiche.
        
        Args:
            lat: Latitudine
            lon: Longitudine
            
        Returns:
            Dict con dati meteorologici o None se errore
        """
        try:
            # Parametri per l'API meteo
            params = {
                'lat': lat,
                'lon': lon,
                'appid': self.api_key,
                'units': 'metric',  # Celsius
                'lang': 'it'
            }
            
            logger.info(f"🌤️ Recupero meteo per coordinate {lat}, {lon}")
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # Estrai i dati rilevanti
            weather_data = {
                'temperature': round(data['main']['temp'], 1),
                'humidity': data['main']['humidity'],
                'pressure': data['main']['pressure'],
                'description': data['weather'][0]['description'],
                'city': data['name'],
                'country': data['sys']['country'],
                'timestamp': datetime.now()
            }
            
            logger.info(f"✅ Dati meteo: {weather_data['temperature']}°C, {weather_data['humidity']}%")
            return weather_data
            
        except requests.RequestException as e:
            logger.error(f"❌ Errore nel recupero meteo: {e}")
            return None
        except KeyError as e:
            logger.error(f"❌ Formato risposta API non valido: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Errore imprevisto nel recupero meteo: {e}")
            return None
    
    def get_weather_by_city(self, city: str, country: str = "IT") -> Optional[Dict[str, Any]]:
        """
        Ottiene i dati meteorologici per una città.
        
        Args:
            city: Nome della città
            country: Codice paese (default: IT)
            
        Returns:
            Dict con temperatura, umidità e altri dati o None se errore
        """
        # Controlla la cache prima
        cache_key = self._get_cache_key(city, country)
        cached_data = self._cache.get(cache_key)
        
        if cached_data and self._is_cache_valid(cached_data):
            logger.info(f"📋 Dati meteo da cache per {city}, {country}")
            return cached_data['data']
        
        try:
            # Prima ottieni le coordinate
            coordinates = self.get_coordinates(city, country)
            if not coordinates:
                logger.warning(f"⚠️ Impossibile ottenere coordinate per {city}, {country}")
                return None
            
            # Poi ottieni i dati meteo
            weather_data = self.get_weather_by_coordinates(
                coordinates['lat'], 
                coordinates['lon']
            )
            
            if weather_data:
                # Salva in cache
                self._cache[cache_key] = {
                    'data': weather_data,
                    'timestamp': datetime.now()
                }
                logger.info(f"💾 Dati meteo salvati in cache per {city}, {country}")
            
            return weather_data
            
        except Exception as e:
            logger.error(f"❌ Errore nel recupero meteo per città: {e}")
            return None
    
    def get_weather_for_cantiere(self, db: Session, cantiere_id: int) -> Optional[Dict[str, Any]]:
        """
        Ottiene i dati meteorologici per un cantiere specifico.
        
        Args:
            db: Sessione database
            cantiere_id: ID del cantiere
            
        Returns:
            Dict con dati meteorologici o None se errore
        """
        try:
            from backend.models.cantiere import Cantiere
            
            # Recupera i dati del cantiere
            cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
            if not cantiere:
                logger.error(f"❌ Cantiere {cantiere_id} non trovato")
                return None
            
            # Verifica che abbia dati di localizzazione
            if not cantiere.citta_cantiere:
                logger.warning(f"⚠️ Cantiere {cantiere_id} non ha città specificata")
                return None
            
            # Usa la nazione del cantiere o default Italia
            country_code = self._get_country_code(cantiere.nazione_cantiere or "Italia")
            
            logger.info(f"🏗️ Recupero meteo per cantiere {cantiere.commessa or cantiere.nome} in {cantiere.citta_cantiere}, {country_code}")
            
            # Ottieni i dati meteo
            weather_data = self.get_weather_by_city(cantiere.citta_cantiere, country_code)
            
            if weather_data:
                # Aggiungi informazioni del cantiere
                weather_data['cantiere_id'] = cantiere_id
                weather_data['cantiere_nome'] = cantiere.commessa or cantiere.nome
                weather_data['cantiere_citta'] = cantiere.citta_cantiere
                weather_data['cantiere_nazione'] = cantiere.nazione_cantiere
            
            return weather_data
            
        except Exception as e:
            logger.error(f"❌ Errore nel recupero meteo per cantiere {cantiere_id}: {e}")
            return None
    
    def _get_country_code(self, country_name: str) -> str:
        """Converte il nome della nazione nel codice paese."""
        country_mapping = {
            'italia': 'IT',
            'italy': 'IT',
            'francia': 'FR',
            'france': 'FR',
            'germania': 'DE',
            'germany': 'DE',
            'spagna': 'ES',
            'spain': 'ES',
            'regno unito': 'GB',
            'united kingdom': 'GB',
            'svizzera': 'CH',
            'switzerland': 'CH',
            'austria': 'AT'
        }
        
        return country_mapping.get(country_name.lower(), 'IT')
    
    def get_demo_weather_data(self) -> Dict[str, Any]:
        """
        Restituisce dati meteo demo per testing quando l'API non è disponibile.
        """
        return {
            'temperature': 22.5,
            'humidity': 65,
            'pressure': 1013,
            'description': 'Sereno',
            'city': 'Demo City',
            'country': 'IT',
            'timestamp': datetime.now(),
            'is_demo': True
        }

# Istanza globale del servizio
weather_service = WeatherService()

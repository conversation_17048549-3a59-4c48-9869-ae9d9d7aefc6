[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "16", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\config.js": "19", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "20", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "21", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "29", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "30", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "31", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "32", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "33", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "34", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "35", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "36", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "37", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "42", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "43", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "44", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "47", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "49", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "51", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "52", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "56", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "58", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "59", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "60", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "61", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "62", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "63", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "64", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "65", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "70", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "71", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "72", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "73", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "74", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "75", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "76", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "82", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "88", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "89", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "90", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "95", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "97", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js": "100", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js": "101"}, {"size": 557, "mtime": 1746952718482, "results": "102", "hashOfConfig": "103"}, {"size": 3196, "mtime": 1748982170834, "results": "104", "hashOfConfig": "103"}, {"size": 996, "mtime": 1746970152489, "results": "105", "hashOfConfig": "103"}, {"size": 10788, "mtime": 1746864244183, "results": "106", "hashOfConfig": "103"}, {"size": 21191, "mtime": 1748751093271, "results": "107", "hashOfConfig": "103"}, {"size": 5121, "mtime": 1749188293819, "results": "108", "hashOfConfig": "103"}, {"size": 2216, "mtime": 1746640055487, "results": "109", "hashOfConfig": "103"}, {"size": 7394, "mtime": 1748034003517, "results": "110", "hashOfConfig": "103"}, {"size": 6749, "mtime": 1746282201800, "results": "111", "hashOfConfig": "103"}, {"size": 15637, "mtime": 1749188445361, "results": "112", "hashOfConfig": "103"}, {"size": 2455, "mtime": 1749188313610, "results": "113", "hashOfConfig": "103"}, {"size": 2050, "mtime": 1746647945415, "results": "114", "hashOfConfig": "103"}, {"size": 700, "mtime": 1747545501078, "results": "115", "hashOfConfig": "103"}, {"size": 14255, "mtime": 1749366744543, "results": "116", "hashOfConfig": "103"}, {"size": 3028, "mtime": 1748816305304, "results": "117", "hashOfConfig": "103"}, {"size": 1630, "mtime": 1746336079554, "results": "118", "hashOfConfig": "103"}, {"size": 1909, "mtime": 1748722592098, "results": "119", "hashOfConfig": "103"}, {"size": 53633, "mtime": 1749188858612, "results": "120", "hashOfConfig": "103"}, {"size": 324, "mtime": 1749199186126, "results": "121", "hashOfConfig": "103"}, {"size": 9068, "mtime": 1746856425683, "results": "122", "hashOfConfig": "103"}, {"size": 2210, "mtime": 1747432283057, "results": "123", "hashOfConfig": "103"}, {"size": 4494, "mtime": 1748121063631, "results": "124", "hashOfConfig": "103"}, {"size": 52535, "mtime": 1749182934135, "results": "125", "hashOfConfig": "103"}, {"size": 3337, "mtime": 1748816346924, "results": "126", "hashOfConfig": "103"}, {"size": 2958, "mtime": 1748816316425, "results": "127", "hashOfConfig": "103"}, {"size": 3507, "mtime": 1748816326922, "results": "128", "hashOfConfig": "103"}, {"size": 3340, "mtime": 1748816336281, "results": "129", "hashOfConfig": "103"}, {"size": 6125, "mtime": 1748705680231, "results": "130", "hashOfConfig": "103"}, {"size": 5880, "mtime": 1748121404574, "results": "131", "hashOfConfig": "103"}, {"size": 3889, "mtime": 1748664890350, "results": "132", "hashOfConfig": "103"}, {"size": 4720, "mtime": 1746771178920, "results": "133", "hashOfConfig": "103"}, {"size": 7681, "mtime": 1749184406942, "results": "134", "hashOfConfig": "103"}, {"size": 10819, "mtime": 1749184481438, "results": "135", "hashOfConfig": "103"}, {"size": 6259, "mtime": 1746965906057, "results": "136", "hashOfConfig": "103"}, {"size": 4215, "mtime": 1746278746358, "results": "137", "hashOfConfig": "103"}, {"size": 1273, "mtime": 1746809069006, "results": "138", "hashOfConfig": "103"}, {"size": 14270, "mtime": 1748371983481, "results": "139", "hashOfConfig": "103"}, {"size": 2752, "mtime": 1747022186740, "results": "140", "hashOfConfig": "103"}, {"size": 1072, "mtime": 1746637929350, "results": "141", "hashOfConfig": "103"}, {"size": 6745, "mtime": 1747545492454, "results": "142", "hashOfConfig": "103"}, {"size": 500, "mtime": 1748722841235, "results": "143", "hashOfConfig": "103"}, {"size": 43883, "mtime": 1749161040576, "results": "144", "hashOfConfig": "103"}, {"size": 1947, "mtime": 1748120984640, "results": "145", "hashOfConfig": "103"}, {"size": 53899, "mtime": 1749153422157, "results": "146", "hashOfConfig": "103"}, {"size": 13911, "mtime": 1749069212408, "results": "147", "hashOfConfig": "103"}, {"size": 18004, "mtime": 1749189211106, "results": "148", "hashOfConfig": "103"}, {"size": 11835, "mtime": 1748920731807, "results": "149", "hashOfConfig": "103"}, {"size": 2211, "mtime": 1748686293878, "results": "150", "hashOfConfig": "103"}, {"size": 9215, "mtime": 1749162481509, "results": "151", "hashOfConfig": "103"}, {"size": 10993, "mtime": 1747154871546, "results": "152", "hashOfConfig": "103"}, {"size": 12217, "mtime": 1749161883257, "results": "153", "hashOfConfig": "103"}, {"size": 20081, "mtime": 1749162690470, "results": "154", "hashOfConfig": "103"}, {"size": 7032, "mtime": 1748069273238, "results": "155", "hashOfConfig": "103"}, {"size": 8589, "mtime": 1748207111023, "results": "156", "hashOfConfig": "103"}, {"size": 10544, "mtime": 1749183282233, "results": "157", "hashOfConfig": "103"}, {"size": 12817, "mtime": 1749183241975, "results": "158", "hashOfConfig": "103"}, {"size": 36555, "mtime": 1747684003188, "results": "159", "hashOfConfig": "103"}, {"size": 9128, "mtime": 1749069292534, "results": "160", "hashOfConfig": "103"}, {"size": 20387, "mtime": 1748984521895, "results": "161", "hashOfConfig": "103"}, {"size": 522, "mtime": 1747022186711, "results": "162", "hashOfConfig": "103"}, {"size": 11907, "mtime": 1749189769410, "results": "163", "hashOfConfig": "103"}, {"size": 7740, "mtime": 1748881233022, "results": "164", "hashOfConfig": "103"}, {"size": 1703, "mtime": 1746972529152, "results": "165", "hashOfConfig": "103"}, {"size": 18402, "mtime": 1749156991134, "results": "166", "hashOfConfig": "103"}, {"size": 12050, "mtime": 1747547543421, "results": "167", "hashOfConfig": "103"}, {"size": 1686, "mtime": 1746946499500, "results": "168", "hashOfConfig": "103"}, {"size": 5145, "mtime": 1746914029633, "results": "169", "hashOfConfig": "103"}, {"size": 10253, "mtime": 1749156772006, "results": "170", "hashOfConfig": "103"}, {"size": 32582, "mtime": 1749161413266, "results": "171", "hashOfConfig": "103"}, {"size": 2574, "mtime": 1748920719208, "results": "172", "hashOfConfig": "103"}, {"size": 4094, "mtime": 1748161663641, "results": "173", "hashOfConfig": "103"}, {"size": 4717, "mtime": 1749142942884, "results": "174", "hashOfConfig": "103"}, {"size": 4346, "mtime": 1747491472989, "results": "175", "hashOfConfig": "103"}, {"size": 15647, "mtime": 1748899398456, "results": "176", "hashOfConfig": "103"}, {"size": 7659, "mtime": 1749366714525, "results": "177", "hashOfConfig": "103"}, {"size": 12341, "mtime": 1749366595552, "results": "178", "hashOfConfig": "103"}, {"size": 15764, "mtime": 1748877145346, "results": "179", "hashOfConfig": "103"}, {"size": 6899, "mtime": 1748877131332, "results": "180", "hashOfConfig": "103"}, {"size": 5536, "mtime": 1748670096009, "results": "181", "hashOfConfig": "103"}, {"size": 5457, "mtime": 1748666884369, "results": "182", "hashOfConfig": "103"}, {"size": 5605, "mtime": 1748666925194, "results": "183", "hashOfConfig": "103"}, {"size": 79333, "mtime": 1749189390406, "results": "184", "hashOfConfig": "103"}, {"size": 2807, "mtime": 1748705699971, "results": "185", "hashOfConfig": "103"}, {"size": 23591, "mtime": 1748881382254, "results": "186", "hashOfConfig": "103"}, {"size": 3708, "mtime": 1748705727900, "results": "187", "hashOfConfig": "103"}, {"size": 10270, "mtime": 1748724524628, "results": "188", "hashOfConfig": "103"}, {"size": 15055, "mtime": 1748755908778, "results": "189", "hashOfConfig": "103"}, {"size": 16415, "mtime": 1748755956687, "results": "190", "hashOfConfig": "103"}, {"size": 3434, "mtime": 1748755857115, "results": "191", "hashOfConfig": "103"}, {"size": 3483, "mtime": 1748755829302, "results": "192", "hashOfConfig": "103"}, {"size": 3508, "mtime": 1748755842942, "results": "193", "hashOfConfig": "103"}, {"size": 956, "mtime": 1748878396989, "results": "194", "hashOfConfig": "103"}, {"size": 13327, "mtime": 1748881322351, "results": "195", "hashOfConfig": "103"}, {"size": 16151, "mtime": 1748981113532, "results": "196", "hashOfConfig": "103"}, {"size": 3613, "mtime": 1748921268108, "results": "197", "hashOfConfig": "103"}, {"size": 1153, "mtime": 1748921279608, "results": "198", "hashOfConfig": "103"}, {"size": 6579, "mtime": 1748922219011, "results": "199", "hashOfConfig": "103"}, {"size": 8976, "mtime": 1748922249445, "results": "200", "hashOfConfig": "103"}, {"size": 29408, "mtime": 1749154958292, "results": "201", "hashOfConfig": "103"}, {"size": 24586, "mtime": 1749155015426, "results": "202", "hashOfConfig": "103"}, {"size": 11668, "mtime": 1749366480246, "results": "203", "hashOfConfig": "103"}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["507"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["508", "509", "510", "511"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["524"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["525", "526", "527", "528", "529", "530", "531", "532", "533", "534"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", ["535"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["551"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["552", "553", "554", "555", "556", "557", "558", "559"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["588"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["589"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["590"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["591", "592", "593", "594"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["595", "596"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["597", "598"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["599", "600", "601", "602"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["603", "604"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["605", "606"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["607", "608"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["609", "610"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["653", "654", "655", "656"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["657", "658", "659", "660", "661", "662", "663", "664", "665"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["666", "667"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["679"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["702"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["703"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["704"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["716", "717", "718", "719"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["734", "735"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["736"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["737", "738"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["756", "757", "758", "759", "760"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["761"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["762", "763", "764"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["765", "766"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["767", "768"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["769"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["770"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["771"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["772", "773", "774", "775", "776", "777", "778"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["779"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["780", "781", "782", "783", "784", "785", "786", "787", "788"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["789", "790", "791", "792", "793", "794", "795", "796", "797"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["798", "799", "800"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["801", "802", "803"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["804", "805", "806", "807"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js", ["808", "809", "810", "811"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js", ["812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js", ["836"], [], {"ruleId": "837", "severity": 1, "message": "838", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 14}, {"ruleId": "841", "severity": 1, "message": "842", "line": 78, "column": 11, "nodeType": "843", "messageId": "844", "endLine": 78, "endColumn": 115}, {"ruleId": "841", "severity": 1, "message": "842", "line": 80, "column": 11, "nodeType": "843", "messageId": "844", "endLine": 80, "endColumn": 107}, {"ruleId": "841", "severity": 1, "message": "842", "line": 86, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 86, "endColumn": 105}, {"ruleId": "841", "severity": 1, "message": "842", "line": 89, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 89, "endColumn": 41}, {"ruleId": "837", "severity": 1, "message": "845", "line": 13, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 13, "endColumn": 9}, {"ruleId": "837", "severity": 1, "message": "846", "line": 20, "column": 25, "nodeType": "839", "messageId": "840", "endLine": 20, "endColumn": 34}, {"ruleId": "837", "severity": 1, "message": "847", "line": 21, "column": 19, "nodeType": "839", "messageId": "840", "endLine": 21, "endColumn": 35}, {"ruleId": "837", "severity": 1, "message": "848", "line": 22, "column": 12, "nodeType": "839", "messageId": "840", "endLine": 22, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "849", "line": 23, "column": 18, "nodeType": "839", "messageId": "840", "endLine": 23, "endColumn": 28}, {"ruleId": "837", "severity": 1, "message": "850", "line": 40, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 40, "endColumn": 35}, {"ruleId": "837", "severity": 1, "message": "851", "line": 40, "column": 37, "nodeType": "839", "messageId": "840", "endLine": 40, "endColumn": 62}, {"ruleId": "837", "severity": 1, "message": "852", "line": 57, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 57, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "853", "line": 58, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 58, "endColumn": 23}, {"ruleId": "837", "severity": 1, "message": "854", "line": 59, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 59, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "855", "line": 60, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 60, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "856", "line": 69, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 69, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "857", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "858", "line": 2, "column": 27, "nodeType": "839", "messageId": "840", "endLine": 2, "endColumn": 31}, {"ruleId": "837", "severity": 1, "message": "859", "line": 2, "column": 33, "nodeType": "839", "messageId": "840", "endLine": 2, "endColumn": 37}, {"ruleId": "837", "severity": 1, "message": "860", "line": 2, "column": 39, "nodeType": "839", "messageId": "840", "endLine": 2, "endColumn": 50}, {"ruleId": "837", "severity": 1, "message": "861", "line": 2, "column": 52, "nodeType": "839", "messageId": "840", "endLine": 2, "endColumn": 66}, {"ruleId": "837", "severity": 1, "message": "845", "line": 2, "column": 68, "nodeType": "839", "messageId": "840", "endLine": 2, "endColumn": 74}, {"ruleId": "837", "severity": 1, "message": "846", "line": 5, "column": 25, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 34}, {"ruleId": "837", "severity": 1, "message": "847", "line": 6, "column": 19, "nodeType": "839", "messageId": "840", "endLine": 6, "endColumn": 35}, {"ruleId": "837", "severity": 1, "message": "848", "line": 7, "column": 12, "nodeType": "839", "messageId": "840", "endLine": 7, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "849", "line": 8, "column": 18, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 28}, {"ruleId": "837", "severity": 1, "message": "862", "line": 43, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 43, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "863", "line": 16, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 16, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "859", "line": 8, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "860", "line": 9, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 9, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "838", "line": 10, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 10, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "858", "line": 11, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "864", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "865", "line": 15, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 15, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "866", "line": 16, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 16, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "867", "line": 17, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 17, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "868", "line": 18, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 18, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "869", "line": 19, "column": 13, "nodeType": "839", "messageId": "840", "endLine": 19, "endColumn": 23}, {"ruleId": "837", "severity": 1, "message": "870", "line": 20, "column": 14, "nodeType": "839", "messageId": "840", "endLine": 20, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "871", "line": 25, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 25, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "872", "line": 28, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 28, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "873", "line": 48, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 48, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "874", "line": 53, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 53, "endColumn": 20}, {"ruleId": "837", "severity": 1, "message": "875", "line": 11, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "876", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "877", "line": 5, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "878", "line": 7, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 7, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "879", "line": 12, "column": 14, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "865", "line": 13, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 13, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "880", "line": 17, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 17, "endColumn": 23}, {"ruleId": "837", "severity": 1, "message": "872", "line": 21, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 21, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "881", "line": 26, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 26, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "859", "line": 8, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "860", "line": 9, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 9, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "878", "line": 11, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "882", "line": 14, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 14, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "883", "line": 26, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 26, "endColumn": 16}, {"ruleId": "837", "severity": 1, "message": "884", "line": 30, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 30, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "885", "line": 32, "column": 14, "nodeType": "839", "messageId": "840", "endLine": 32, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "886", "line": 33, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 33, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "887", "line": 34, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 34, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "888", "line": 35, "column": 27, "nodeType": "839", "messageId": "840", "endLine": 35, "endColumn": 51}, {"ruleId": "837", "severity": 1, "message": "889", "line": 42, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 42, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "890", "line": 50, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 50, "endColumn": 24}, {"ruleId": "837", "severity": 1, "message": "891", "line": 51, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 51, "endColumn": 16}, {"ruleId": "837", "severity": 1, "message": "872", "line": 62, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 62, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "892", "line": 63, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 63, "endColumn": 32}, {"ruleId": "837", "severity": 1, "message": "850", "line": 63, "column": 34, "nodeType": "839", "messageId": "840", "endLine": 63, "endColumn": 58}, {"ruleId": "837", "severity": 1, "message": "893", "line": 63, "column": 60, "nodeType": "839", "messageId": "840", "endLine": 63, "endColumn": 82}, {"ruleId": "837", "severity": 1, "message": "851", "line": 63, "column": 84, "nodeType": "839", "messageId": "840", "endLine": 63, "endColumn": 109}, {"ruleId": "837", "severity": 1, "message": "894", "line": 63, "column": 111, "nodeType": "839", "messageId": "840", "endLine": 63, "endColumn": 133}, {"ruleId": "837", "severity": 1, "message": "895", "line": 63, "column": 135, "nodeType": "839", "messageId": "840", "endLine": 63, "endColumn": 160}, {"ruleId": "837", "severity": 1, "message": "896", "line": 64, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 64, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "881", "line": 66, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 66, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "897", "line": 260, "column": 19, "nodeType": "839", "messageId": "840", "endLine": 260, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "898", "line": 268, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 268, "endColumn": 28}, {"ruleId": "837", "severity": 1, "message": "899", "line": 269, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 269, "endColumn": 23}, {"ruleId": "837", "severity": 1, "message": "900", "line": 269, "column": 25, "nodeType": "839", "messageId": "840", "endLine": 269, "endColumn": 41}, {"ruleId": "901", "severity": 1, "message": "902", "line": 629, "column": 6, "nodeType": "903", "endLine": 629, "endColumn": 15, "suggestions": "904"}, {"ruleId": "837", "severity": 1, "message": "905", "line": 674, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 674, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "906", "line": 1, "column": 27, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 36}, {"ruleId": "837", "severity": 1, "message": "907", "line": 49, "column": 19, "nodeType": "839", "messageId": "840", "endLine": 49, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "908", "line": 178, "column": 6, "nodeType": "903", "endLine": 178, "endColumn": 38, "suggestions": "909"}, {"ruleId": "837", "severity": 1, "message": "876", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "877", "line": 5, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "881", "line": 26, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 26, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "910", "line": 48, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 48, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "876", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "910", "line": 37, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 37, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "876", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "910", "line": 52, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 52, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "876", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "877", "line": 5, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "881", "line": 26, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 26, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "910", "line": 48, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 48, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "872", "line": 24, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 24, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "911", "line": 53, "column": 6, "nodeType": "903", "endLine": 53, "endColumn": 18, "suggestions": "912"}, {"ruleId": "837", "severity": 1, "message": "913", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "914", "line": 5, "column": 7, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "864", "line": 14, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 14, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "915", "line": 28, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 28, "endColumn": 18}, {"ruleId": "837", "severity": 1, "message": "913", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "914", "line": 5, "column": 7, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "859", "line": 8, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "860", "line": 9, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 9, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "838", "line": 10, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 10, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "916", "line": 23, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 23, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "917", "line": 24, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 24, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "864", "line": 25, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 25, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "878", "line": 29, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 29, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "918", "line": 30, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 30, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "919", "line": 31, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 31, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "920", "line": 32, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 32, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "921", "line": 33, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 33, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "922", "line": 34, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 34, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "923", "line": 35, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 35, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "868", "line": 39, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 39, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "866", "line": 43, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 43, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "924", "line": 44, "column": 14, "nodeType": "839", "messageId": "840", "endLine": 44, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "925", "line": 50, "column": 69, "nodeType": "839", "messageId": "840", "endLine": 50, "endColumn": 76}, {"ruleId": "837", "severity": 1, "message": "926", "line": 79, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 79, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "927", "line": 161, "column": 6, "nodeType": "903", "endLine": 161, "endColumn": 8, "suggestions": "928"}, {"ruleId": "837", "severity": 1, "message": "929", "line": 675, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 675, "endColumn": 26}, {"ruleId": "841", "severity": 1, "message": "842", "line": 260, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 264, "endColumn": 11}, {"ruleId": "841", "severity": 1, "message": "842", "line": 274, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 274, "endColumn": 70}, {"ruleId": "841", "severity": 1, "message": "842", "line": 278, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 278, "endColumn": 54}, {"ruleId": "841", "severity": 1, "message": "842", "line": 333, "column": 11, "nodeType": "843", "messageId": "844", "endLine": 338, "endColumn": 13}, {"ruleId": "841", "severity": 1, "message": "842", "line": 435, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 439, "endColumn": 11}, {"ruleId": "841", "severity": 1, "message": "842", "line": 451, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 451, "endColumn": 54}, {"ruleId": "841", "severity": 1, "message": "842", "line": 668, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 668, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 677, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 677, "endColumn": 70}, {"ruleId": "841", "severity": 1, "message": "842", "line": 681, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 681, "endColumn": 54}, {"ruleId": "837", "severity": 1, "message": "930", "line": 755, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 755, "endColumn": 22}, {"ruleId": "841", "severity": 1, "message": "842", "line": 775, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 779, "endColumn": 11}, {"ruleId": "841", "severity": 1, "message": "842", "line": 794, "column": 11, "nodeType": "843", "messageId": "844", "endLine": 798, "endColumn": 13}, {"ruleId": "841", "severity": 1, "message": "842", "line": 801, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 804, "endColumn": 11}, {"ruleId": "841", "severity": 1, "message": "842", "line": 810, "column": 11, "nodeType": "843", "messageId": "844", "endLine": 814, "endColumn": 13}, {"ruleId": "841", "severity": 1, "message": "842", "line": 817, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 820, "endColumn": 11}, {"ruleId": "841", "severity": 1, "message": "842", "line": 885, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 889, "endColumn": 11}, {"ruleId": "931", "severity": 1, "message": "932", "line": 955, "column": 3, "nodeType": "933", "messageId": "934", "endLine": 955, "endColumn": 29}, {"ruleId": "931", "severity": 1, "message": "935", "line": 1143, "column": 3, "nodeType": "933", "messageId": "934", "endLine": 1143, "endColumn": 23}, {"ruleId": "841", "severity": 1, "message": "842", "line": 1252, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 1252, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 1282, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 1282, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 1335, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 1335, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 1382, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 1382, "endColumn": 163}, {"ruleId": "837", "severity": 1, "message": "936", "line": 6, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 6, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "864", "line": 11, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "937", "line": 20, "column": 13, "nodeType": "839", "messageId": "840", "endLine": 20, "endColumn": 23}, {"ruleId": "837", "severity": 1, "message": "938", "line": 205, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 205, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "876", "line": 2, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 2, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "939", "line": 2, "column": 64, "nodeType": "839", "messageId": "840", "endLine": 2, "endColumn": 70}, {"ruleId": "837", "severity": 1, "message": "887", "line": 4, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "940", "line": 5, "column": 12, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "941", "line": 6, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 6, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "889", "line": 7, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 7, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "942", "line": 8, "column": 16, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "943", "line": 14, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 14, "endColumn": 20}, {"ruleId": "837", "severity": 1, "message": "944", "line": 121, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 121, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "913", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "914", "line": 5, "column": 7, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "945", "line": 3, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 3, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "946", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 6}, {"ruleId": "837", "severity": 1, "message": "947", "line": 5, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "948", "line": 6, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 6, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "949", "line": 7, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 7, "endColumn": 6}, {"ruleId": "837", "severity": 1, "message": "950", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 9}, {"ruleId": "837", "severity": 1, "message": "951", "line": 36, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 36, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "952", "line": 50, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 50, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "953", "line": 64, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 64, "endColumn": 20}, {"ruleId": "837", "severity": 1, "message": "954", "line": 88, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 88, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "955", "line": 104, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 104, "endColumn": 30}, {"ruleId": "837", "severity": 1, "message": "956", "line": 3, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 3, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "948", "line": 3, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 3, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "949", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 6}, {"ruleId": "837", "severity": 1, "message": "957", "line": 5, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "958", "line": 6, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 6, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "959", "line": 7, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 7, "endColumn": 16}, {"ruleId": "837", "severity": 1, "message": "960", "line": 8, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "950", "line": 9, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 9, "endColumn": 9}, {"ruleId": "837", "severity": 1, "message": "961", "line": 10, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 10, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "945", "line": 11, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "946", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 6}, {"ruleId": "837", "severity": 1, "message": "947", "line": 13, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 13, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "962", "line": 14, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 14, "endColumn": 16}, {"ruleId": "837", "severity": 1, "message": "963", "line": 15, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 15, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "956", "line": 16, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 16, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "964", "line": 18, "column": 40, "nodeType": "839", "messageId": "840", "endLine": 18, "endColumn": 44}, {"ruleId": "837", "severity": 1, "message": "965", "line": 47, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 47, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "966", "line": 64, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 64, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "967", "line": 71, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 71, "endColumn": 20}, {"ruleId": "837", "severity": 1, "message": "954", "line": 79, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 79, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "955", "line": 95, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 95, "endColumn": 30}, {"ruleId": "837", "severity": 1, "message": "968", "line": 272, "column": 27, "nodeType": "839", "messageId": "840", "endLine": 272, "endColumn": 37}, {"ruleId": "837", "severity": 1, "message": "969", "line": 273, "column": 27, "nodeType": "839", "messageId": "840", "endLine": 273, "endColumn": 36}, {"ruleId": "837", "severity": 1, "message": "877", "line": 3, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 3, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "970", "line": 54, "column": 6, "nodeType": "903", "endLine": 54, "endColumn": 34, "suggestions": "971"}, {"ruleId": "837", "severity": 1, "message": "972", "line": 25, "column": 13, "nodeType": "839", "messageId": "840", "endLine": 25, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "973", "line": 33, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 33, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "974", "line": 34, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 34, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "975", "line": 35, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 35, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "976", "line": 36, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 36, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "977", "line": 37, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 37, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "978", "line": 41, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 41, "endColumn": 20}, {"ruleId": "837", "severity": 1, "message": "979", "line": 43, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 43, "endColumn": 34}, {"ruleId": "837", "severity": 1, "message": "980", "line": 69, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 69, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "981", "line": 69, "column": 19, "nodeType": "839", "messageId": "840", "endLine": 69, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "982", "line": 88, "column": 6, "nodeType": "903", "endLine": 88, "endColumn": 18, "suggestions": "983"}, {"ruleId": "901", "severity": 1, "message": "984", "line": 448, "column": 6, "nodeType": "903", "endLine": 448, "endColumn": 28, "suggestions": "985"}, {"ruleId": "837", "severity": 1, "message": "863", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "937", "line": 21, "column": 20, "nodeType": "839", "messageId": "840", "endLine": 21, "endColumn": 30}, {"ruleId": "837", "severity": 1, "message": "938", "line": 100, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 100, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "986", "line": 119, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 119, "endColumn": 30}, {"ruleId": "837", "severity": 1, "message": "987", "line": 8, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "988", "line": 9, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 9, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "989", "line": 10, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 10, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "990", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 9}, {"ruleId": "837", "severity": 1, "message": "991", "line": 13, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 13, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "992", "line": 14, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 14, "endColumn": 16}, {"ruleId": "837", "severity": 1, "message": "993", "line": 15, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 15, "endColumn": 16}, {"ruleId": "837", "severity": 1, "message": "994", "line": 36, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 36, "endColumn": 30}, {"ruleId": "837", "severity": 1, "message": "995", "line": 37, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 37, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "970", "line": 46, "column": 6, "nodeType": "903", "endLine": 46, "endColumn": 18, "suggestions": "996"}, {"ruleId": "837", "severity": 1, "message": "997", "line": 265, "column": 23, "nodeType": "839", "messageId": "840", "endLine": 265, "endColumn": 44}, {"ruleId": "837", "severity": 1, "message": "998", "line": 266, "column": 23, "nodeType": "839", "messageId": "840", "endLine": 266, "endColumn": 42}, {"ruleId": "837", "severity": 1, "message": "997", "line": 381, "column": 21, "nodeType": "839", "messageId": "840", "endLine": 381, "endColumn": 42}, {"ruleId": "837", "severity": 1, "message": "998", "line": 382, "column": 21, "nodeType": "839", "messageId": "840", "endLine": 382, "endColumn": 40}, {"ruleId": "837", "severity": 1, "message": "913", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "914", "line": 5, "column": 7, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "999", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "913", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "914", "line": 5, "column": 7, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "913", "line": 1, "column": 8, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "914", "line": 5, "column": 7, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "1000", "line": 83, "column": 13, "nodeType": "839", "messageId": "840", "endLine": 83, "endColumn": 21}, {"ruleId": "841", "severity": 1, "message": "842", "line": 109, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 109, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 123, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 123, "endColumn": 70}, {"ruleId": "841", "severity": 1, "message": "842", "line": 127, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 127, "endColumn": 54}, {"ruleId": "841", "severity": 1, "message": "842", "line": 212, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 212, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 226, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 226, "endColumn": 70}, {"ruleId": "841", "severity": 1, "message": "842", "line": 230, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 230, "endColumn": 54}, {"ruleId": "841", "severity": 1, "message": "842", "line": 271, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 271, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 280, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 280, "endColumn": 70}, {"ruleId": "841", "severity": 1, "message": "842", "line": 284, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 284, "endColumn": 54}, {"ruleId": "841", "severity": 1, "message": "842", "line": 320, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 320, "endColumn": 70}, {"ruleId": "841", "severity": 1, "message": "842", "line": 324, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 324, "endColumn": 54}, {"ruleId": "841", "severity": 1, "message": "842", "line": 416, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 416, "endColumn": 163}, {"ruleId": "841", "severity": 1, "message": "842", "line": 425, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 425, "endColumn": 70}, {"ruleId": "841", "severity": 1, "message": "842", "line": 429, "column": 9, "nodeType": "843", "messageId": "844", "endLine": 429, "endColumn": 54}, {"ruleId": "837", "severity": 1, "message": "980", "line": 60, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 60, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "981", "line": 60, "column": 19, "nodeType": "839", "messageId": "840", "endLine": 60, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "970", "line": 90, "column": 6, "nodeType": "903", "endLine": 90, "endColumn": 32, "suggestions": "1001"}, {"ruleId": "837", "severity": 1, "message": "1002", "line": 370, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 370, "endColumn": 23}, {"ruleId": "837", "severity": 1, "message": "1003", "line": 470, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 470, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "1004", "line": 17, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 17, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "1005", "line": 16, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 16, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "1006", "line": 17, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 17, "endColumn": 9}, {"ruleId": "837", "severity": 1, "message": "1007", "line": 19, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 19, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "883", "line": 14, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 14, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "1008", "line": 43, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 43, "endColumn": 26}, {"ruleId": "837", "severity": 1, "message": "878", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "1009", "line": 25, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 25, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "1010", "line": 33, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 33, "endColumn": 29}, {"ruleId": "837", "severity": 1, "message": "1011", "line": 3, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 3, "endColumn": 6}, {"ruleId": "837", "severity": 1, "message": "864", "line": 9, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 9, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "1012", "line": 20, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 20, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "972", "line": 58, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 58, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "1013", "line": 137, "column": 6, "nodeType": "903", "endLine": 137, "endColumn": 18, "suggestions": "1014"}, {"ruleId": "901", "severity": 1, "message": "1015", "line": 142, "column": 6, "nodeType": "903", "endLine": 142, "endColumn": 52, "suggestions": "1016"}, {"ruleId": "901", "severity": 1, "message": "1017", "line": 147, "column": 6, "nodeType": "903", "endLine": 147, "endColumn": 62, "suggestions": "1018"}, {"ruleId": "901", "severity": 1, "message": "1019", "line": 152, "column": 6, "nodeType": "903", "endLine": 152, "endColumn": 28, "suggestions": "1020"}, {"ruleId": "901", "severity": 1, "message": "1021", "line": 161, "column": 6, "nodeType": "903", "endLine": 161, "endColumn": 39, "suggestions": "1022"}, {"ruleId": "901", "severity": 1, "message": "1023", "line": 68, "column": 6, "nodeType": "903", "endLine": 68, "endColumn": 18, "suggestions": "1024"}, {"ruleId": "837", "severity": 1, "message": "906", "line": 1, "column": 27, "nodeType": "839", "messageId": "840", "endLine": 1, "endColumn": 36}, {"ruleId": "837", "severity": 1, "message": "859", "line": 10, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 10, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "860", "line": 11, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "876", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "1004", "line": 27, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 27, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "867", "line": 30, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 30, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "1025", "line": 33, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 33, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "849", "line": 34, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 34, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "879", "line": 35, "column": 14, "nodeType": "839", "messageId": "840", "endLine": 35, "endColumn": 25}, {"ruleId": "837", "severity": 1, "message": "859", "line": 10, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 10, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "860", "line": 11, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "1004", "line": 27, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 27, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "1026", "line": 28, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 28, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "1027", "line": 29, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 29, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "1028", "line": 30, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 30, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "867", "line": 34, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 34, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "1029", "line": 37, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 37, "endColumn": 31}, {"ruleId": "901", "severity": 1, "message": "1030", "line": 98, "column": 6, "nodeType": "903", "endLine": 98, "endColumn": 24, "suggestions": "1031"}, {"ruleId": "837", "severity": 1, "message": "859", "line": 4, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 4, "endColumn": 7}, {"ruleId": "837", "severity": 1, "message": "860", "line": 5, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1032", "line": 66, "column": 6, "nodeType": "903", "endLine": 66, "endColumn": 25, "suggestions": "1033"}, {"ruleId": "837", "severity": 1, "message": "1034", "line": 196, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 196, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "1035", "line": 233, "column": 11, "nodeType": "839", "messageId": "840", "endLine": 233, "endColumn": 24}, {"ruleId": "901", "severity": 1, "message": "1036", "line": 389, "column": 6, "nodeType": "903", "endLine": 389, "endColumn": 58, "suggestions": "1037"}, {"ruleId": "837", "severity": 1, "message": "1038", "line": 15, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 15, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "1007", "line": 16, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 16, "endColumn": 13}, {"ruleId": "837", "severity": 1, "message": "1006", "line": 17, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 17, "endColumn": 9}, {"ruleId": "837", "severity": 1, "message": "1005", "line": 18, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 18, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "989", "line": 21, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 21, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "1039", "line": 28, "column": 12, "nodeType": "839", "messageId": "840", "endLine": 28, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "978", "line": 33, "column": 10, "nodeType": "839", "messageId": "840", "endLine": 33, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "1040", "line": 78, "column": 9, "nodeType": "839", "messageId": "840", "endLine": 78, "endColumn": 24}, {"ruleId": "837", "severity": 1, "message": "918", "line": 8, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 8, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "919", "line": 9, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 9, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "920", "line": 10, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 10, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "921", "line": 11, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 11, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "922", "line": 12, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 12, "endColumn": 12}, {"ruleId": "837", "severity": 1, "message": "923", "line": 13, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 13, "endColumn": 11}, {"ruleId": "837", "severity": 1, "message": "1004", "line": 15, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 15, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "864", "line": 25, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 25, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "989", "line": 30, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 30, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "960", "line": 32, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 32, "endColumn": 10}, {"ruleId": "837", "severity": 1, "message": "936", "line": 33, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 33, "endColumn": 8}, {"ruleId": "837", "severity": 1, "message": "937", "line": 40, "column": 13, "nodeType": "839", "messageId": "840", "endLine": 40, "endColumn": 23}, {"ruleId": "837", "severity": 1, "message": "889", "line": 42, "column": 15, "nodeType": "839", "messageId": "840", "endLine": 42, "endColumn": 27}, {"ruleId": "837", "severity": 1, "message": "973", "line": 50, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 50, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "974", "line": 51, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 51, "endColumn": 14}, {"ruleId": "837", "severity": 1, "message": "975", "line": 52, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 52, "endColumn": 22}, {"ruleId": "837", "severity": 1, "message": "976", "line": 53, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 53, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "977", "line": 54, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 54, "endColumn": 17}, {"ruleId": "837", "severity": 1, "message": "1041", "line": 55, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 55, "endColumn": 15}, {"ruleId": "837", "severity": 1, "message": "1042", "line": 56, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 56, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "1043", "line": 57, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 57, "endColumn": 21}, {"ruleId": "837", "severity": 1, "message": "978", "line": 58, "column": 3, "nodeType": "839", "messageId": "840", "endLine": 58, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "1044", "line": 96, "column": 6, "nodeType": "903", "endLine": 96, "endColumn": 32, "suggestions": "1045"}, {"ruleId": "837", "severity": 1, "message": "1046", "line": 223, "column": 13, "nodeType": "839", "messageId": "840", "endLine": 223, "endColumn": 19}, {"ruleId": "837", "severity": 1, "message": "1009", "line": 21, "column": 17, "nodeType": "839", "messageId": "840", "endLine": 21, "endColumn": 29}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'setOpenEliminaCavoDialog' is assigned a value but never used.", "'setOpenModificaCavoDialog' is assigned a value but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'TextField' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CheckBoxIcon' is defined but never used.", "'CheckBoxOutlineBlankIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'parcoCaviService' is defined but never used.", "'CavoForm' is defined but never used.", "'openEliminaCavoDialog' is assigned a value but never used.", "'openModificaCavoDialog' is assigned a value but never used.", "'openAggiungiCavoDialog' is assigned a value but never used.", "'setOpenAggiungiCavoDialog' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1047"], "'getAllSelectedCavi' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStoricoBobine'. Either include it or remove the dependency array.", ["1048"], "'handleBackToCantieri' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1049"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1050"], "'renderBobineCards' is assigned a value but never used.", "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "'Stack' is defined but never used.", "'CancelIcon' is defined but never used.", "'handleCancel' is assigned a value but never used.", "'Button' is defined but never used.", "'ClearIcon' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'formatDate' is defined but never used.", "'handleClearSelection' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1051"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1052"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1053"], "'handleBackToSelection' is assigned a value but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'internalSelectedCavo' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", ["1054"], "'latoPartenzaCollegato' is assigned a value but never used.", "'latoArrivoCollegato' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", ["1055"], "'result' is assigned a value but never used.", "'hasMetri' is assigned a value but never used.", "'Alert' is defined but never used.", "'MenuItem' is defined but never used.", "'Select' is defined but never used.", "'InputLabel' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'LocationIcon' is defined but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1056"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1057"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1058"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1059"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1060"], "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1061"], "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1062"], "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1063"], "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1064"], "'FormControl' is defined but never used.", "'CloseIcon' is defined but never used.", "'getBobinaNumber' is assigned a value but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1065"], "'bobina' is assigned a value but never used.", {"desc": "1066", "fix": "1067"}, {"desc": "1068", "fix": "1069"}, {"desc": "1070", "fix": "1071"}, {"desc": "1072", "fix": "1073"}, {"desc": "1074", "fix": "1075"}, {"desc": "1076", "fix": "1077"}, {"desc": "1078", "fix": "1079"}, {"desc": "1080", "fix": "1081"}, {"desc": "1082", "fix": "1083"}, {"desc": "1084", "fix": "1085"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"desc": "1090", "fix": "1091"}, {"desc": "1092", "fix": "1093"}, {"desc": "1094", "fix": "1095"}, {"desc": "1096", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1100", "fix": "1101"}, {"desc": "1102", "fix": "1103"}, "Update the dependencies array to be: [caviAttivi, caviSpare, error, filters, user]", {"range": "1104", "text": "1105"}, "Update the dependencies array to be: [cantiereId, loadStoricoBobine, selectedReportType]", {"range": "1106", "text": "1107"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1108", "text": "1109"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1110", "text": "1111"}, "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1112", "text": "1113"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1114", "text": "1115"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1116", "text": "1117"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1118", "text": "1119"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1120", "text": "1121"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1122", "text": "1123"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1126", "text": "1127"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1128", "text": "1129"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1134", "text": "1135"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1136", "text": "1137"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1138", "text": "1139"}, "Update the dependencies array to be: [open, cavoPreselezionato, loadBobine]", {"range": "1140", "text": "1141"}, [25162, 25171], "[caviAttivi, caviSpare, error, filters, user]", [5510, 5542], "[cantiere<PERSON>d, loadStoricoBobine, selectedReportType]", [1559, 1571], "[cantiereId, selectCantiere]", [5793, 5795], "[handleOptionSelect, initialOption, loadBobine]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1077, 1089], "[cantiereId, loadCavi]", [2734, 2760], "[open, bobina, cantiereId, loadCavi]", [3636, 3648], "[cantiereId, loadInitialData]", [3733, 3779], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [3863, 3919], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [4025, 4047], "[calculateStatistics, cavi, certificazioni]", [4269, 4302], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1648, 1660], "[cantiereId, loadComande, loadStatistiche]", [2516, 2534], "[certificazioneId, loadProve]", [1523, 1542], "[loadCaviDisponibili, open, tipoComanda]", [11274, 11326], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", [2440, 2466], "[open, cavoPreselezionato, loadBobine]"]
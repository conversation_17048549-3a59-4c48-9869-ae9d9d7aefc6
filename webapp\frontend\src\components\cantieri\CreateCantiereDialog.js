import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  Divider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Construction as ConstructionIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import cantieriService from '../../services/cantieriService';

/**
 * Dialog per la creazione di un nuovo cantiere con localizzazione geografica
 */
const CreateCantiereDialog = ({
  open,
  onClose,
  onCantiereCreated = null
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [formData, setFormData] = useState({
    commessa: '',
    descrizione: '',
    nome_cliente: '',
    indirizzo_cantiere: '',
    citta_cantiere: '',
    nazione_cantiere: 'Italia',
    password_cantiere: '',
    conferma_password: '',
    riferimenti_normativi: 'CEI 64-8 Parte 6; IEC 60364-6',
    documentazione_progetto: 'Schemi elettrici e layout di progetto'
  });

  // Gestisce i cambiamenti nei campi del form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Gestisce la chiusura del dialog
  const handleClose = () => {
    if (!loading) {
      setFormData({
        commessa: '',
        descrizione: '',
        nome_cliente: '',
        indirizzo_cantiere: '',
        citta_cantiere: '',
        nazione_cantiere: 'Italia',
        password_cantiere: '',
        conferma_password: '',
        riferimenti_normativi: 'CEI 64-8 Parte 6; IEC 60364-6',
        documentazione_progetto: 'Schemi elettrici e layout di progetto'
      });
      setError('');
      setSuccess('');
      onClose();
    }
  };

  // Gestisce l'invio del form
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validazione
    if (!formData.commessa.trim()) {
      setError('Il nome della commessa è obbligatorio');
      return;
    }
    
    if (!formData.password_cantiere) {
      setError('La password del cantiere è obbligatoria');
      return;
    }
    
    if (formData.password_cantiere !== formData.conferma_password) {
      setError('Le password non coincidono');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const newCantiere = await cantieriService.createCantiere({
        commessa: formData.commessa.trim(),
        descrizione: formData.descrizione.trim() || null,
        nome_cliente: formData.nome_cliente.trim() || null,
        indirizzo_cantiere: formData.indirizzo_cantiere.trim() || null,
        citta_cantiere: formData.citta_cantiere.trim() || null,
        nazione_cantiere: formData.nazione_cantiere.trim() || 'Italia',
        password_cantiere: formData.password_cantiere,
        riferimenti_normativi: formData.riferimenti_normativi.trim() || null,
        documentazione_progetto: formData.documentazione_progetto.trim() || null
      });
      
      setSuccess('Cantiere creato con successo!');
      
      // Notifica il componente padre
      if (onCantiereCreated) {
        onCantiereCreated(newCantiere);
      }
      
      // Chiudi il dialog dopo un breve delay
      setTimeout(() => {
        handleClose();
      }, 1500);
      
    } catch (err) {
      console.error('Errore durante la creazione del cantiere:', err);
      setError(err.detail || err.message || 'Errore durante la creazione del cantiere');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ConstructionIcon />
          <Typography variant="h6">
            Crea Nuovo Cantiere
          </Typography>
        </Box>
      </DialogTitle>
      
      <form onSubmit={handleSubmit}>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {/* Sezione Informazioni Generali */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ 
              fontWeight: 'bold', 
              color: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <DescriptionIcon fontSize="small" />
              Informazioni Generali
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome Commessa"
                  name="commessa"
                  value={formData.commessa}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                  placeholder="Es. Ampliamento Impianto XPTO"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Descrizione"
                  name="descrizione"
                  value={formData.descrizione}
                  onChange={handleInputChange}
                  multiline
                  rows={2}
                  disabled={loading}
                  placeholder="Descrizione dettagliata del progetto"
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Sezione Cliente e Localizzazione */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ 
              fontWeight: 'bold', 
              color: 'primary.main',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <BusinessIcon fontSize="small" />
              Cliente e Localizzazione
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Nome Cliente"
                  name="nome_cliente"
                  value={formData.nome_cliente}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Es. Azienda Elettrica SpA"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Nazione</InputLabel>
                  <Select
                    name="nazione_cantiere"
                    value={formData.nazione_cantiere}
                    onChange={handleInputChange}
                    disabled={loading}
                    label="Nazione"
                  >
                    <MenuItem value="Italia">Italia</MenuItem>
                    <MenuItem value="Francia">Francia</MenuItem>
                    <MenuItem value="Germania">Germania</MenuItem>
                    <MenuItem value="Spagna">Spagna</MenuItem>
                    <MenuItem value="Regno Unito">Regno Unito</MenuItem>
                    <MenuItem value="Svizzera">Svizzera</MenuItem>
                    <MenuItem value="Austria">Austria</MenuItem>
                    <MenuItem value="Altro">Altro</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Città"
                  name="citta_cantiere"
                  value={formData.citta_cantiere}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Es. Milano"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Indirizzo Cantiere"
                  name="indirizzo_cantiere"
                  value={formData.indirizzo_cantiere}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Es. Via Roma 123"
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Sezione Sicurezza */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ 
              fontWeight: 'bold', 
              color: 'primary.main'
            }}>
              Sicurezza
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Password Cantiere"
                  name="password_cantiere"
                  type="password"
                  value={formData.password_cantiere}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Conferma Password"
                  name="conferma_password"
                  type="password"
                  value={formData.conferma_password}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                />
              </Grid>
            </Grid>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Sezione Normative */}
          <Box>
            <Typography variant="subtitle1" gutterBottom sx={{ 
              fontWeight: 'bold', 
              color: 'primary.main'
            }}>
              Normative e Documentazione
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Riferimenti Normativi"
                  name="riferimenti_normativi"
                  value={formData.riferimenti_normativi}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Es. CEI 64-8 Parte 6; IEC 60364-6"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Documentazione Progetto"
                  name="documentazione_progetto"
                  value={formData.documentazione_progetto}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="Es. Schemi elettrici e layout di progetto"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button 
            onClick={handleClose} 
            disabled={loading}
          >
            Annulla
          </Button>
          <Button 
            type="submit"
            variant="contained" 
            disabled={loading}
          >
            {loading ? 'Creazione...' : 'Crea Cantiere'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateCantiereDialog;
